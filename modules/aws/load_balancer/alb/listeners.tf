# ALB Listeners (using for_each for hash-based naming)
resource "aws_lb_listener" "this" {
  for_each = local.all_listeners

  load_balancer_arn = aws_lb.alb.arn
  port              = each.value.port
  protocol          = each.value.protocol
  ssl_policy        = each.value.protocol == "HTTPS" || each.value.protocol == "TLS" ? each.value.ssl_policy : null
  alpn_policy       = each.value.protocol == "TLS" ? each.value.alpn_policy : null
  
  # Use listener-specific certificate or global certificate
  certificate_arn = (
    each.value.certificate_arn != null ? each.value.certificate_arn : 
    (each.value.protocol == "HTTPS" || each.value.protocol == "TLS") ? local.certificate_arn : null
  )

  # Default action
  dynamic "default_action" {
    for_each = [each.value.default_action]
    content {
      type = default_action.value.type

      # Forward action
      dynamic "forward" {
        for_each = default_action.value.type == "forward" && default_action.value.target_group_key != null ? [1] : []
        content {
          target_group {
            arn = aws_lb_target_group.this[default_action.value.target_group_key].arn
          }
        }
      }

      # Redirect action
      dynamic "redirect" {
        for_each = default_action.value.type == "redirect" && default_action.value.redirect != null ? [default_action.value.redirect] : []
        content {
          host        = redirect.value.host
          path        = redirect.value.path
          port        = redirect.value.port
          protocol    = redirect.value.protocol
          query       = redirect.value.query
          status_code = redirect.value.status_code
        }
      }

      # Fixed response action
      dynamic "fixed_response" {
        for_each = default_action.value.type == "fixed-response" && default_action.value.fixed_response != null ? [default_action.value.fixed_response] : []
        content {
          content_type = fixed_response.value.content_type
          message_body = fixed_response.value.message_body
          status_code  = fixed_response.value.status_code
        }
      }
    }
  }

  tags = merge(var.tags_basic, var.tags_extra, {
    Name = "${var.environment}-${var.service_name}-listener-${each.key}"
  })

  depends_on = [
    aws_acm_certificate_validation.acm_certificate_validation
  ]
}

# Listener Rules (using for_each for hash-based naming)
resource "aws_lb_listener_rule" "this" {
  for_each = {
    for rule in flatten([
      for listener_key, listener_config in local.all_listeners : [
        for rule_idx, rule_config in listener_config.rules : {
          key          = "${listener_key}-rule-${rule_config.priority}"
          listener_arn = aws_lb_listener.this[listener_key].arn
          priority     = rule_config.priority
          conditions   = rule_config.conditions
          actions      = rule_config.actions
        }
      ]
    ]) : rule.key => rule
  }

  listener_arn = each.value.listener_arn
  priority     = each.value.priority

  # Conditions
  dynamic "condition" {
    for_each = each.value.conditions
    content {
      # Host header condition
      dynamic "host_header" {
        for_each = condition.value.field == "host-header" ? [1] : []
        content {
          values = condition.value.values
        }
      }

      # Path pattern condition
      dynamic "path_pattern" {
        for_each = condition.value.field == "path-pattern" ? [1] : []
        content {
          values = condition.value.values
        }
      }

      # HTTP header condition
      dynamic "http_header" {
        for_each = condition.value.field == "http-header" && condition.value.http_header != null ? [condition.value.http_header] : []
        content {
          http_header_name = http_header.value.http_header_name
          values           = http_header.value.values
        }
      }

      # HTTP request method condition
      dynamic "http_request_method" {
        for_each = condition.value.field == "http-request-method" ? [1] : []
        content {
          values = condition.value.values
        }
      }

      # Query string condition
      dynamic "query_string" {
        for_each = condition.value.field == "query-string" && condition.value.query_string != null ? condition.value.query_string : []
        content {
          key   = query_string.value.key
          value = query_string.value.value
        }
      }

      # Source IP condition
      dynamic "source_ip" {
        for_each = condition.value.field == "source-ip" ? [1] : []
        content {
          values = condition.value.values
        }
      }
    }
  }

  # Actions
  dynamic "action" {
    for_each = each.value.actions
    content {
      type = action.value.type

      # Forward action
      dynamic "forward" {
        for_each = action.value.type == "forward" && action.value.target_group_key != null ? [1] : []
        content {
          target_group {
            arn = aws_lb_target_group.this[action.value.target_group_key].arn
          }
        }
      }

      # Redirect action
      dynamic "redirect" {
        for_each = action.value.type == "redirect" && action.value.redirect != null ? [action.value.redirect] : []
        content {
          host        = redirect.value.host
          path        = redirect.value.path
          port        = redirect.value.port
          protocol    = redirect.value.protocol
          query       = redirect.value.query
          status_code = redirect.value.status_code
        }
      }

      # Fixed response action
      dynamic "fixed_response" {
        for_each = action.value.type == "fixed-response" && action.value.fixed_response != null ? [action.value.fixed_response] : []
        content {
          content_type = fixed_response.value.content_type
          message_body = fixed_response.value.message_body
          status_code  = fixed_response.value.status_code
        }
      }
    }
  }

  tags = merge(var.tags_basic, var.tags_extra, {
    Name = "${var.environment}-${var.service_name}-rule-${each.key}"
  })
}
