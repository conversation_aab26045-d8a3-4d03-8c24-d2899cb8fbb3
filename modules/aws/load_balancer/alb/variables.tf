variable "logs_prefix" {
  default = ""
}

variable "logs_enabled" {
  default = true
}

variable "internal" {
  default = true
}

variable "enable_deletion_protection" {
  default = false
}

variable "enable_cross_zone_load_balancing" {
  default = true
}

variable "tags_basic" {
  type = map(string)

  default = {}
}
variable "tags_extra" {
  type = map(string)

  default = {}
}

#---------------------------------------------------
# Security Group
#---------------------------------------------------

variable "security_group_name" {
  description = "Security group for loadbalancer"
  type        = string
}

variable "security_group_rules" {}

#---------------------------------------------------
# Target Group
#---------------------------------------------------
variable "vpc_id" {
  type    = string
  default = ""
}

#---------------------------------------------------
# SSL Certificate Configuration
#---------------------------------------------------
variable "cert_domain" {
  description = "Domain of the certificate (used only when creating new ACM certificate)"
  type        = string
  default     = ""
}

variable "certificate_arn" {
  description = "ARN of existing SSL certificate. If provided, ACM certificate creation and DNS validation will be skipped"
  type        = string
  default     = ""
}

variable "create_certificate" {
  description = "Whether to create a new ACM certificate. Set to false when using existing certificate_arn"
  type        = bool
  default     = true
}

#---------------------------------------------------
# Target Groups Configuration (Multiple Support)
#---------------------------------------------------
variable "target_groups" {
  description = "Map of target groups to create"
  type = map(object({
    name                          = string
    port                          = number
    protocol                      = string
    target_type                   = string
    health_check_port             = optional(string, "traffic-port")
    health_check_protocol         = optional(string, "HTTP")
    health_check_path             = optional(string, "/")
    health_check_matcher          = optional(string, "200")
    healthy_threshold             = optional(number, 2)
    unhealthy_threshold           = optional(number, 2)
    health_check_interval         = optional(number, 30)
    health_check_timeout          = optional(number, 5)
    deregistration_delay          = optional(number, 300)
    load_balancing_algorithm_type = optional(string, "round_robin")
    targets = optional(list(object({
      id                = string
      port              = optional(number)
      availability_zone = optional(string)
    })), [])
  }))
  default = {}
}

# Legacy variables for backward compatibility
variable "tg_name" {
  description = "[DEPRECATED] Use target_groups instead. Name of the target group"
  type        = string
  default     = ""
}

variable "tg_port" {
  description = "[DEPRECATED] Use target_groups instead. Port for the target group"
  type        = number
  default     = 443
}

variable "tg_protocol" {
  description = "[DEPRECATED] Use target_groups instead. Protocol for the target group"
  type        = string
  default     = "HTTPS"
}

variable "tg_target_type" {
  description = "[DEPRECATED] Use target_groups instead. Target Group Type"
  type        = string
  default     = "instance"
}

variable "health_check_port" {
  description = "[DEPRECATED] Use target_groups instead. Health check port for the target"
  type        = string
  default     = "443"
}

variable "health_check_protocol" {
  description = "[DEPRECATED] Use target_groups instead. Health check protocol for the target"
  type        = string
  default     = "HTTPS"
}

variable "target_id" {
  description = "[DEPRECATED] Use target_groups instead. ID of the target to attach with ALB"
  default     = ""
}

variable "az" {
  description = "[DEPRECATED] Use target_groups instead. Availability Zone Details"
  default     = ""
}

#---------------------------------------------------
# Listeners Configuration (Multiple Support)
#---------------------------------------------------
variable "listeners" {
  description = "Map of listeners to create"
  type = map(object({
    port            = number
    protocol        = string
    ssl_policy      = optional(string, "ELBSecurityPolicy-TLS13-1-2-2021-06")
    alpn_policy     = optional(string)
    certificate_arn = optional(string) # Override global certificate
    default_action = object({
      type             = string # forward, redirect, fixed-response
      target_group_key = optional(string) # Key from target_groups map
      redirect = optional(object({
        host        = optional(string)
        path        = optional(string)
        port        = optional(string)
        protocol    = optional(string)
        query       = optional(string)
        status_code = string
      }))
      fixed_response = optional(object({
        content_type = string
        message_body = optional(string)
        status_code  = string
      }))
    })
    rules = optional(list(object({
      priority = number
      conditions = list(object({
        field  = string # host-header, path-pattern, http-header, http-request-method, query-string, source-ip
        values = list(string)
        http_header = optional(object({
          http_header_name = string
          values           = list(string)
        }))
        query_string = optional(list(object({
          key   = optional(string)
          value = string
        })))
      }))
      actions = list(object({
        type             = string # forward, redirect, fixed-response
        target_group_key = optional(string)
        redirect = optional(object({
          host        = optional(string)
          path        = optional(string)
          port        = optional(string)
          protocol    = optional(string)
          query       = optional(string)
          status_code = string
        }))
        fixed_response = optional(object({
          content_type = string
          message_body = optional(string)
          status_code  = string
        }))
      }))
    })), [])
  }))
  default = {}
}

# Legacy variables for backward compatibility
variable "listener_port" {
  description = "[DEPRECATED] Use listeners instead. Listener Port"
  type        = string
  default     = "443"
}

variable "listener_protocol" {
  description = "[DEPRECATED] Use listeners instead. Listener Protocol"
  type        = string
  default     = "HTTPS"
}

variable "enable_http_listener" {
  description = "[DEPRECATED] Use listeners instead. Enable HTTP listener"
  type        = bool
  default     = false
}

#---------------------------------------------------
# S3 Bucket Access Logs
#---------------------------------------------------
variable "versioning" {
  default = false
}

variable "enable_bucket_replication" {
  type    = bool
  default = false
}

variable "destination_kms_key_arn" {
  type    = string
  default = ""
}

variable "s3_bucket_destination_arn" {
  description = "ARN of destination S3 Bucket you're copying the files to"
  type        = string
  default     = ""
}

#---------------------------------------------------
# General Variables
#---------------------------------------------------
variable "environment" {
  description = "The deployment environment"
  type        = string
}

variable "account_name" {
  description = "Name of Account"
}

variable "service_name" {
  description = "Name of Service"
  type        = string
}

variable "route53_zone" {
  description = "DNS Zone name"
  type        = string
}

variable "enable_route53_alb" {
  type    = bool
  default = true
}

variable "subnet_ids" {
  description = "List of subnet IDs to attach to the load balancer. Should be either public or private subnets."
  type = list(string)
}
