locals {
  access_logs_bucket_name = "tf-${var.account_name}-${var.environment}-sysops-logs"

  # Certificate management logic
  use_existing_certificate = var.certificate_arn != ""
  should_create_certificate = var.create_certificate && !local.use_existing_certificate && var.cert_domain != ""

  # Final certificate ARN to use
  certificate_arn = local.use_existing_certificate ? var.certificate_arn : (
    local.should_create_certificate ? aws_acm_certificate.acm_certificate[0].arn : ""
  )

  # Backward compatibility: merge legacy configuration with new target_groups
  legacy_target_group = var.tg_name != "" ? {
    legacy = {
      name                          = var.tg_name
      port                          = var.tg_port
      protocol                      = var.tg_protocol
      target_type                   = var.tg_target_type
      health_check_port             = var.health_check_port
      health_check_protocol         = var.health_check_protocol
      health_check_path             = "/"
      health_check_matcher          = "200"
      healthy_threshold             = 2
      unhealthy_threshold           = 2
      health_check_interval         = 30
      health_check_timeout          = 5
      deregistration_delay          = 300
      load_balancing_algorithm_type = "round_robin"
      targets = var.target_id != "" ? [{
        id                = var.target_id
        port              = var.tg_port
        availability_zone = var.az != "" ? var.az : null
      }] : []
    }
  } : {}

  # Merge legacy and new target groups
  all_target_groups = merge(local.legacy_target_group, var.target_groups)

  # Backward compatibility: merge legacy listener configuration with new listeners
  legacy_listeners = var.tg_name != "" ? {
    https = {
      port            = tonumber(var.listener_port)
      protocol        = var.listener_protocol
      ssl_policy      = "ELBSecurityPolicy-TLS13-1-2-2021-06"
      certificate_arn = null # Use global certificate
      default_action = {
        type             = "forward"
        target_group_key = "legacy"
      }
      rules = []
    }
  } : {}

  # Add HTTP listener if enabled (legacy)
  legacy_http_listener = var.enable_http_listener && var.tg_name != "" ? {
    http = {
      port       = 80
      protocol   = "HTTP"
      ssl_policy = null
      default_action = {
        type             = "forward"
        target_group_key = "legacy"
      }
      rules = []
    }
  } : {}

  # Merge all listeners
  all_listeners = merge(local.legacy_listeners, local.legacy_http_listener, var.listeners)
}