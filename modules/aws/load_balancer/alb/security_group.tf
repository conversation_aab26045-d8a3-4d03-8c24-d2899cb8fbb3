# Security Group for ALB (using for_each for hash-based naming)
module "load_balancer_sg" {
  for_each = var.security_group_name != "" ? { main = true } : {}

  source               = "../../security-group/v1"
  security_group_name  = "${var.environment}-${var.account_name}-${var.security_group_name}-sg"
  vpc_id               = data.aws_vpc.selected.id
  security_group_rules = var.security_group_rules
}