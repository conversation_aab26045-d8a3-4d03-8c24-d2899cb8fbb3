# Target Groups (using for_each for hash-based naming)
resource "aws_lb_target_group" "this" {
  for_each = local.all_target_groups

  name                          = each.value.name
  port                          = each.value.port
  protocol                      = each.value.protocol
  vpc_id                        = data.aws_vpc.selected.id
  target_type                   = each.value.target_type
  deregistration_delay          = each.value.deregistration_delay
  load_balancing_algorithm_type = each.value.load_balancing_algorithm_type

  health_check {
    enabled             = true
    healthy_threshold   = each.value.healthy_threshold
    unhealthy_threshold = each.value.unhealthy_threshold
    timeout             = each.value.health_check_timeout
    interval            = each.value.health_check_interval
    path                = each.value.health_check_protocol == "HTTP" || each.value.health_check_protocol == "HTTPS" ? each.value.health_check_path : null
    matcher             = each.value.health_check_protocol == "HTTP" || each.value.health_check_protocol == "HTTPS" ? each.value.health_check_matcher : null
    port                = each.value.health_check_port
    protocol            = each.value.health_check_protocol
  }

  tags = merge(var.tags_basic, var.tags_extra, {
    Name = each.value.name
  })

  lifecycle {
    create_before_destroy = true
  }
}

# Target Group Attachments (using for_each for hash-based naming)
resource "aws_lb_target_group_attachment" "this" {
  for_each = {
    for pair in flatten([
      for tg_key, tg_config in local.all_target_groups : [
        for idx, target in tg_config.targets : {
          key               = "${tg_key}-${idx}"
          target_group_arn  = aws_lb_target_group.this[tg_key].arn
          target_id         = target.id
          port              = target.port != null ? target.port : (tg_config.target_type == "lambda" ? null : tg_config.port)
          availability_zone = target.availability_zone
        }
      ]
    ]) : pair.key => pair
  }

  target_group_arn  = each.value.target_group_arn
  target_id         = each.value.target_id
  port              = each.value.port
  availability_zone = each.value.availability_zone
}
