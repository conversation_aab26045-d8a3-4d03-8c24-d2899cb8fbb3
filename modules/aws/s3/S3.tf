resource "aws_s3_bucket" "bucket" {
  bucket = var.bucket_name

  tags = var.tags
}

resource "aws_s3_bucket_versioning" "bucket" {
  count = local.create_bucket && var.versioning ? 1 : 0

  bucket                = aws_s3_bucket.bucket.id
  expected_bucket_owner = var.expected_bucket_owner

  versioning_configuration {
    status = var.versioning ? "Enabled" : "Disabled"
  }
}

resource "aws_s3_bucket_ownership_controls" "bucket" {
  count = local.create_bucket && var.control_object_ownership ? 1 : 0

  bucket = aws_s3_bucket.bucket.id

  rule {
    object_ownership = var.object_ownership
  }

  # This `depends_on` is to prevent "A conflicting conditional operation is currently in progress against this resource."
  depends_on = [
    aws_s3_bucket_policy.private,
    aws_s3_bucket_public_access_block.private,
    aws_s3_bucket.bucket
  ]
}

resource "aws_s3_bucket_acl" "bucket" {
  count = local.create_bucket && local.create_bucket_acl ? 1 : 0

  bucket                = aws_s3_bucket.bucket.id
  expected_bucket_owner = var.expected_bucket_owner

  # hack when `null` value can't be used (eg, from terragrunt, https://github.com/gruntwork-io/terragrunt/pull/1367)
  acl = var.bucket_acl == "null" ? null : var.bucket_acl

  dynamic "access_control_policy" {
    for_each = length(local.grants) > 0 ? [true] : []

    content {
      dynamic "grant" {
        for_each = local.grants

        content {
          permission = grant.value.permission

          grantee {
            type          = grant.value.type
            id            = try(grant.value.id, null)
            uri           = try(grant.value.uri, null)
            email_address = try(grant.value.email, null)
          }
        }
      }

      owner {
        id           = try(var.owner["id"], data.aws_canonical_user_id.this[0].id)
        display_name = try(var.owner["display_name"], null)
      }
    }
  }

  # This `depends_on` is to prevent "AccessControlListNotSupported: The bucket does not allow ACLs."
  depends_on = [aws_s3_bucket_ownership_controls.bucket]
}

resource "aws_s3_bucket_public_access_block" "private" {
  count  = var.block_public_access ? 1 : 0
  bucket = aws_s3_bucket.bucket.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
  depends_on              = [aws_s3_bucket_policy.private]
}

resource "aws_s3_bucket_policy" "private" {
  count  = var.create_bucket_policy ? 1 : 0
  bucket = aws_s3_bucket.bucket.id
  policy = data.aws_iam_policy_document.combined.json
}

resource "aws_s3_bucket_logging" "logging" {
  count  = var.enable_s3_bucket_logging ? 1 : 0
  bucket = aws_s3_bucket.bucket.id

  target_bucket = "${var.logging_bucket_prefix}-${data.aws_caller_identity.current.account_id}"
  target_prefix = var.logging_prefix
}

data "aws_iam_policy_document" "combined" {
  source_policy_documents   = compact(concat([data.aws_iam_policy_document.deny_insecure_transport.json, var.s3_bucket_policy], var.source_s3_policy_documents))
  override_policy_documents = var.override_s3_policy_documents
}

# reference: https://docs.aws.amazon.com/AmazonS3/latest/userguide/configure-request-metrics-bucket.html
resource "aws_s3_bucket_metric" "extra_metric_requirements" {
  count  = var.enable_extra_metric ? 1 : 0
  bucket = aws_s3_bucket.bucket.bucket
  name   = "${aws_s3_bucket.bucket.bucket}-metric"
}

module "sns_notification" {
  source  = "terraform-aws-modules/s3-bucket/aws//modules/notification"
  version = "v3.15.1"

  create            = var.create_notification
  create_sns_policy = var.create_notification_sns_policy
  create_sqs_policy = var.create_notification_sqs_policy

  bucket = aws_s3_bucket.bucket.id

  eventbridge = var.eventbridge

  sns_notifications = merge({
    sns = {
      topic_arn = module.sns_topic.sns_topic_arn
      events = [
        "s3:ObjectCreated:*",
        "s3:ObjectRemoved:*",
      ]
    }
  }, var.sns_notifications)
  sqs_notifications    = var.sqs_notifications
  lambda_notifications = var.lambda_notifications
}

resource "aws_s3_bucket_server_side_encryption_configuration" "sse" {
  count = length(keys(var.server_side_encryption_configuration)) > 0 ? 1 : 0

  bucket                = aws_s3_bucket.bucket.id
  expected_bucket_owner = var.expected_bucket_owner

  dynamic "rule" {
    for_each = try(flatten([var.server_side_encryption_configuration["rule"]]), [])

    content {
      bucket_key_enabled = try(rule.value.bucket_key_enabled, null)

      dynamic "apply_server_side_encryption_by_default" {
        for_each = try([rule.value.apply_server_side_encryption_by_default], [])

        content {
          sse_algorithm     = apply_server_side_encryption_by_default.value.sse_algorithm
          kms_master_key_id = try(apply_server_side_encryption_by_default.value.kms_master_key_id, "alias/s3-${local.bucket_name}")
        }
      }
    }
  }
}

resource "aws_s3_bucket_replication_configuration" "replication" {
  count      = var.enable_bucket_replication ? 1 : 0
  depends_on = [aws_s3_bucket.bucket]

  role   = aws_iam_role.replication[count.index].arn
  bucket = aws_s3_bucket.bucket.id

  rule {
    id = "all_object_replication"

    filter {
      prefix = var.s3_replication_prefix
    }

    status = "Enabled"

    destination {
      bucket        = var.s3_bucket_destination_arn
      storage_class = "STANDARD"

      encryption_configuration {
        replica_kms_key_id = var.destination_kms_key_arn
      }
    }

    source_selection_criteria {
      sse_kms_encrypted_objects {
        status = "Enabled"
      }
    }
    delete_marker_replication {
      status = "Enabled"
    }
  }
}

resource "aws_s3_bucket_cors_configuration" "cors" {
  count      = var.enable_bucket_cors ? 1 : 0
  depends_on = [aws_s3_bucket.bucket]

  bucket = aws_s3_bucket.bucket.id
  cors_rule {
    allowed_headers = var.cors_allowed_headers
    allowed_methods = var.cors_allowed_methods
    allowed_origins = var.cors_allowed_origins
    max_age_seconds = var.cors_max_age_seconds
  }
}

resource "aws_s3_bucket_intelligent_tiering_configuration" "bucket" {
  for_each = { for k, v in local.intelligent_tiering : k => v if local.create_bucket }

  name   = each.key
  bucket = aws_s3_bucket.bucket.id
  status = try(tobool(each.value.status) ? "Enabled" : "Disabled", title(lower(each.value.status)), null)

  # Max 1 block - filter
  dynamic "filter" {
    for_each = length(try(flatten([each.value.filter]), [])) == 0 ? [] : [true]

    content {
      prefix = try(each.value.filter.prefix, null)
      tags   = try(each.value.filter.tags, null)
    }
  }

  dynamic "tiering" {
    for_each = each.value.tiering

    content {
      access_tier = tiering.key
      days        = tiering.value.days
    }
  }
}

resource "aws_s3_bucket_lifecycle_configuration" "bucket" {
  count = local.create_bucket && length(local.lifecycle_rules) > 0 ? 1 : 0

  bucket                = aws_s3_bucket.bucket.id
  expected_bucket_owner = var.expected_bucket_owner

  dynamic "rule" {
    for_each = local.lifecycle_rules

    content {
      id     = try(rule.value.id, null)
      status = try(rule.value.enabled ? "Enabled" : "Disabled", tobool(rule.value.status) ? "Enabled" : "Disabled", title(lower(rule.value.status)))

      # Max 1 block - abort_incomplete_multipart_upload
      dynamic "abort_incomplete_multipart_upload" {
        for_each = try([rule.value.abort_incomplete_multipart_upload_days], [])

        content {
          days_after_initiation = try(rule.value.abort_incomplete_multipart_upload_days, null)
        }
      }


      # Max 1 block - expiration
      dynamic "expiration" {
        for_each = try(flatten([rule.value.expiration]), [])

        content {
          date                         = try(expiration.value.date, null)
          days                         = try(expiration.value.days, null)
          expired_object_delete_marker = try(expiration.value.expired_object_delete_marker, null)
        }
      }

      # Several blocks - transition
      dynamic "transition" {
        for_each = try(flatten([rule.value.transition]), [])

        content {
          date          = try(transition.value.date, null)
          days          = try(transition.value.days, null)
          storage_class = transition.value.storage_class
        }
      }

      # Max 1 block - noncurrent_version_expiration
      dynamic "noncurrent_version_expiration" {
        for_each = try(flatten([rule.value.noncurrent_version_expiration]), [])

        content {
          newer_noncurrent_versions = try(noncurrent_version_expiration.value.newer_noncurrent_versions, null)
          noncurrent_days           = try(noncurrent_version_expiration.value.days, noncurrent_version_expiration.value.noncurrent_days, null)
        }
      }

      # Several blocks - noncurrent_version_transition
      dynamic "noncurrent_version_transition" {
        for_each = try(flatten([rule.value.noncurrent_version_transition]), [])

        content {
          newer_noncurrent_versions = try(noncurrent_version_transition.value.newer_noncurrent_versions, null)
          noncurrent_days           = try(noncurrent_version_transition.value.days, noncurrent_version_transition.value.noncurrent_days, null)
          storage_class             = noncurrent_version_transition.value.storage_class
        }
      }

      # Max 1 block - filter - without any key arguments or tags
      dynamic "filter" {
        for_each = length(try(flatten([rule.value.filter]), [])) == 0 ? [true] : []

        content {
          #          prefix = ""
        }
      }

      # Max 1 block - filter - with one key argument or a single tag
      dynamic "filter" {
        for_each = [for v in try(flatten([rule.value.filter]), []) : v if max(length(keys(v)), length(try(rule.value.filter.tags, rule.value.filter.tag, []))) == 1]

        content {
          object_size_greater_than = try(filter.value.object_size_greater_than, null)
          object_size_less_than    = try(filter.value.object_size_less_than, null)
          prefix                   = try(filter.value.prefix, null)

          dynamic "tag" {
            for_each = try(filter.value.tags, filter.value.tag, [])

            content {
              key   = tag.key
              value = tag.value
            }
          }
        }
      }

      # Max 1 block - filter - with more than one key arguments or multiple tags
      dynamic "filter" {
        for_each = [for v in try(flatten([rule.value.filter]), []) : v if max(length(keys(v)), length(try(rule.value.filter.tags, rule.value.filter.tag, []))) > 1]

        content {
          and {
            object_size_greater_than = try(filter.value.object_size_greater_than, null)
            object_size_less_than    = try(filter.value.object_size_less_than, null)
            prefix                   = try(filter.value.prefix, null)
            tags                     = try(filter.value.tags, filter.value.tag, null)
          }
        }
      }
    }
  }

  # Must have bucket versioning enabled first
  depends_on = [aws_s3_bucket_versioning.bucket]
}

resource "aws_s3_bucket_object_lock_configuration" "bucket" {
  count = local.create_bucket && var.object_lock_enabled && try(var.object_lock_configuration.rule.default_retention, null) != null ? 1 : 0

  bucket                = aws_s3_bucket.bucket.id
  expected_bucket_owner = var.expected_bucket_owner
  token                 = try(var.object_lock_configuration.token, null)

  rule {
    default_retention {
      mode  = var.object_lock_configuration.rule.default_retention.mode
      days  = try(var.object_lock_configuration.rule.default_retention.days, null)
      years = try(var.object_lock_configuration.rule.default_retention.years, null)
    }
  }
}
