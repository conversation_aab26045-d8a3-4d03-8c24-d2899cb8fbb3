################################################################################
# KMS Key
################################################################################

resource "aws_kms_key" "kms_key" {
  description = "Used for encrypting ${var.bucket_name} s3 bucket resources (s3, sns)"
  policy      = data.aws_iam_policy_document.iam_policy_document.json

  enable_key_rotation = true
  tags                = var.tags
}

resource "aws_kms_alias" "kms_alias" {
  name          = "alias/s3-${local.bucket_name}"
  target_key_id = aws_kms_key.kms_key.key_id
}

data "aws_iam_policy_document" "iam_policy_document" {
  statement {
    sid = "Grant Root Permissions"

    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"]
    }

    actions = [
      "kms:*"
    ]

    resources = [
      "*"
    ]
  }

  statement {
    sid = "Allow S3 decrypt SNS data"

    principals {
      type        = "Service"
      identifiers = ["s3.amazonaws.com"]
    }

    actions = [
      "kms:GenerateDataKey",
      "kms:Decrypt"
    ]

    resources = [
      "*"
    ]
  }

  statement {
    sid = "Allow S3 encrypt data"

    principals {
      type        = "Service"
      identifiers = ["s3.amazonaws.com"]
    }

    actions = [
      "kms:Encrypt"
    ]

    resources = [
      "*"
    ]
  }

  dynamic "statement" {
    for_each = length(var.cross_account_role) > 0 ? [1] : []
    content {
      sid = "Allow cross account role to decrypt/encrypt s3 object"
      principals {
        type        = "AWS"
        identifiers = var.cross_account_role
      }
      actions = [
        "kms:GenerateDataKey",
        "kms:Encrypt",
        "kms:Decrypt"
      ]
      resources = ["*"]
    }
  }

  dynamic "statement" {
    # loop over log_delivery_sources and convert to map. the key is concat of account_id and region
    for_each = { for source in var.log_delivery_sources : "${source.account_id}-${source.region}" => source }
    content {
      sid = "Allow log delivery to decrypt/encrypt s3 object"
      principals {
        type        = "Service"
        identifiers = ["delivery.logs.amazonaws.com"]
      }
      actions = [
        "kms:Encrypt",
        "kms:Decrypt",
        "kms:ReEncrypt*",
        "kms:GenerateDataKey*",
        "kms:DescribeKey"
      ]
      resources = ["*"]
      condition {
        test     = "StringEquals"
        variable = "aws:SourceAccount"
        values   = [statement.value.account_id]
      }
      condition {
        test     = "ArnLike"
        variable = "aws:SourceArn"
        values   = ["arn:aws:logs:${statement.value.region}:${statement.value.account_id}:delivery-source:*"]
      }
    }
  }
}
