#---------------------------------------------------
# Assume Role
#---------------------------------------------------
resource "aws_iam_role" "replication" {
  count              = var.enable_bucket_replication ? 1 : 0
  name               = "${var.bucket_name}-iam-role-replication"
  assume_role_policy = data.aws_iam_policy_document.assume_role[count.index].json
}

data "aws_iam_policy_document" "assume_role" {
  count = var.enable_bucket_replication ? 1 : 0
  statement {
    effect = "Allow"

    principals {
      type        = "Service"
      identifiers = ["s3.amazonaws.com"]
    }

    actions = ["sts:AssumeRole"]
  }
}

#---------------------------------------------------
# Attach Permission for Cross Account Replication
#---------------------------------------------------
data "aws_iam_policy_document" "replication" {
  count = var.enable_bucket_replication ? 1 : 0
  statement {
    effect = "Allow"

    actions = [
      "s3:GetObjectVersion",
      "s3:GetObjectRetention",
      "s3:GetObjectVersionTagging",
      "s3:GetObjectVersionAcl",
      "s3:ListBucket",
      "s3:GetObjectVersionForReplication",
      "s3:GetObjectLegalHold",
      "s3:GetReplicationConfiguration"
    ]
    resources = [
      aws_s3_bucket.bucket.arn,
      "${aws_s3_bucket.bucket.arn}/*"
    ]
  }

  statement {
    effect = "Allow"
    actions = [
      "s3:GetBucketVersioning"
    ]
    resources = [aws_s3_bucket.bucket.arn, var.s3_bucket_destination_arn]
  }

  statement {
    effect = "Allow"
    actions = [
      "s3:ReplicateObject",
      "s3:ObjectOwnerOverrideToBucketOwner",
      "s3:GetObjectVersionTagging",
      "s3:ReplicateTags",
      "s3:ReplicateDelete"
    ]

    resources = ["${var.s3_bucket_destination_arn}/*"]
  }

  statement {
    effect    = "Allow"
    actions   = ["kms:Decrypt"]
    resources = [aws_kms_key.kms_key.arn]
  }

  statement {
    effect    = "Allow"
    actions   = ["kms:GenerateDataKey"]
    resources = [aws_kms_key.kms_key.arn, var.destination_kms_key_arn]
  }

  statement {
    effect    = "Allow"
    actions   = ["kms:Encrypt"]
    resources = [var.destination_kms_key_arn]
  }
}

resource "aws_iam_policy" "replication" {
  count  = var.enable_bucket_replication ? 1 : 0
  name   = "${var.bucket_name}-iam-role-policy-replication"
  policy = data.aws_iam_policy_document.replication[count.index].json
}

resource "aws_iam_role_policy_attachment" "replication" {
  count      = var.enable_bucket_replication ? 1 : 0
  role       = aws_iam_role.replication[count.index].name
  policy_arn = aws_iam_policy.replication[count.index].arn
}