variable "create_bucket" {
  description = "Controls if S3 bucket should be created"
  type        = bool
  default     = true
}

variable "bucket_acl" {
  description = "(Optional) The canned ACL to apply. Conflicts with `grant`"
  type        = string
  default     = null
}

variable "bucket_name" {}

variable "bucket_region" {
  default = "ap-southeast-5"
}

variable "versioning" {
  default = false
}
variable "s3_bucket_policy" {}

variable "tags" {
  default = {}
}

variable "lifecycle_rules" {
  default = []
}

variable "enable_extra_metric" {
  description = "Whether to enable extra metrics for monitoring. For example, see 4xx errors, 5xx errors. Reference: https://docs.aws.amazon.com/AmazonS3/latest/userguide/configure-request-metrics-bucket.html"
  type        = bool
  default     = false
}

variable "block_public_access" {
  description = "Whether to block public access for s3 buckets"
  type        = bool
  default     = true
}

variable "lifecycle_prevent_destroy" {
  type    = bool
  default = true
}

variable "s3_bucket_logging_bucket" {
  type    = string
  default = ""
}

variable "create_bucket_policy" {
  default = true
}

variable "logging_bucket_prefix" {
  default = "tabspace-s3-logs"
}

variable "logging_prefix" {
  default = "log/"
}

variable "owner" {
  description = "Bucket owner's display name and ID. Conflicts with `acl`"
  type        = map(string)
  default     = {}
}

variable "expected_bucket_owner" {
  description = "The account ID of the expected bucket owner"
  type        = string
  default     = null
}

variable "server_side_encryption_configuration" {
  description = "Map containing server-side encryption configuration."
  type        = any
  default     = {}
}

variable "s3_bucket_destination_arn" {
  description = "ARN of destination S3 Bucket you're copying the files to"
  type        = string
  default     = ""
}

variable "enable_bucket_replication" {
  type    = bool
  default = false
}

variable "s3_replication_prefix" {
  type    = string
  default = ""
}

variable "destination_kms_key_arn" {
  type    = string
  default = ""
}

variable "cross_account_role" {
  type    = list(string)
  default = []
}

variable "enable_bucket_cors" {
  type    = bool
  default = false
}
variable "cors_allowed_headers" {
  type    = list(string)
  default = []
}

variable "cors_allowed_methods" {
  type    = list(string)
  default = []
}

variable "cors_allowed_origins" {
  type    = list(string)
  default = []
}

variable "cors_max_age_seconds" {
  type    = number
  default = 3000
}

variable "object_lock_enabled" {
  description = "Whether S3 bucket should have an Object Lock configuration enabled."
  type        = bool
  default     = false
}

variable "control_object_ownership" {
  description = "Whether to manage S3 Bucket Ownership Controls on this bucket."
  type        = bool
  default     = false
}

variable "object_ownership" {
  description = "Object ownership. Valid values: BucketOwnerEnforced, BucketOwnerPreferred or ObjectWriter. 'BucketOwnerEnforced': ACLs are disabled, and the bucket owner automatically owns and has full control over every object in the bucket. 'BucketOwnerPreferred': Objects uploaded to the bucket change ownership to the bucket owner if the objects are uploaded with the bucket-owner-full-control canned ACL. 'ObjectWriter': The uploading account will own the object if the object is uploaded with the bucket-owner-full-control canned ACL."
  type        = string
  default     = "BucketOwnerEnforced"
}

variable "grant" {
  description = "An ACL policy grant. Conflicts with `acl`"
  type        = any
  default     = []
}

variable "source_s3_policy_documents" {
  description = "List of IAM policy documents that are merged together into the exported document. Statements must have unique `sid`s"
  type        = list(string)
  default     = []
}

variable "override_s3_policy_documents" {
  description = "List of IAM policy documents that are merged together into the exported document. In merging, statements with non-blank `sid`s will override statements with the same `sid`"
  type        = list(string)
  default     = []
}

variable "intelligent_tiering" {
  description = "Map containing intelligent tiering configuration."
  type        = any
  default     = {}
}

variable "object_lock_configuration" {
  description = "Map containing S3 object locking configuration."
  type        = any
  default     = {}
}

variable "create_notification" {
  description = "Whether to create this resource or not?"
  type        = bool
  default     = true
}

variable "create_notification_sns_policy" {
  description = "Whether to create a policy for SNS permissions or not?"
  type        = bool
  default     = true
}

variable "create_notification_sqs_policy" {
  description = "Whether to create a policy for SQS permissions or not?"
  type        = bool
  default     = true
}

variable "eventbridge" {
  description = "Whether to enable Amazon EventBridge notifications"
  type        = bool
  default     = null
}

variable "sns_notifications" {
  description = "Map of S3 bucket notifications to SNS topic"
  type        = any
  default     = {}
}

variable "sqs_notifications" {
  description = "Map of S3 bucket notifications to SQS queue"
  type        = any
  default     = {}
}

variable "lambda_notifications" {
  description = "Map of S3 bucket notifications to Lambda function"
  type        = any
  default     = {}
}
variable "enable_s3_bucket_logging" {
  description = "Boolean value that determines if s3 bucket logging is enabled"
  type        = bool
  default     = true
}
variable "log_delivery_sources" {
  description = "List of source buckets to deliver logs to this bucket"
  type        = list(object({
    account_id = string
    region     = string
  }))
  default     = []
}
