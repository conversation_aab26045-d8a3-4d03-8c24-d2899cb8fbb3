################################################################################
# S3 bucket event notification SNS
################################################################################

module "sns_topic" {
  source  = "terraform-aws-modules/sns/aws"
  version = "v4.1.0"

  name              = "s3-${local.bucket_name}-events"
  kms_master_key_id = "alias/s3-${local.bucket_name}"
  tags              = var.tags

  depends_on = [
    aws_kms_key.kms_key
  ]
}
