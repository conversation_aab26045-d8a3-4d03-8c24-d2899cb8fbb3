# AWS RDS Terraform module

We will be using this [module](https://github.com/terraform-aws-modules/terraform-aws-rds/tree/master) to create RDS instances. There are some [examples](https://github.com/terraform-aws-modules/terraform-aws-rds/tree/master/examples) that you can refer to create the RDS deployment of your choice.

Important notes:

We will need to provide rds master username and password during the terraform apply. To avoid saving the password in the state file, please use AWS secret manager to create a secret and reference it in the module.

```terraform

resource "random_password" "rds" {
  length  = 16
  special = true
}

resource "aws_secretsmanager_secret" "rds_password" {
  name = "db-name-password"
}

resource "aws_secretsmanager_secret_version" "rds" {
  secret_id     = aws_secretsmanager_secret.rds_password.id
  secret_string = random_password.rds.result
}

# Reference the secret in the module using aws_secretsmanager_secret_version.rds.secret_string
# Remember to grant your service role access to read the secret.
```
