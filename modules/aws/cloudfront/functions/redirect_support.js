// Based on https://docs.aws.amazon.com/AmazonCloudFront/latest/DeveloperGuide/example_cloudfront_functions_redirect_based_on_country_section.html
async function handler(event) {
  let response = event.response;
  let headers = response.headers;
  headers["content-security-policy"] = {
    value: "${CSP}",
  };

  if (headers["x-amz-website-redirect-location"]) {
    let redirectRelativePath = headers["x-amz-website-redirect-location"].value;
    let newurl = `https://${websiteDomain}${redirectRelativePath}`;
    response.statusCode = 301;
    headers["location"] = { value: newurl };
  }

  return response;
}
