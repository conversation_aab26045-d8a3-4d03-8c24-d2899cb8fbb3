// Based on https://docs.aws.amazon.com/AmazonCloudFront/latest/DeveloperGuide/example_cloudfront_functions_redirect_based_on_country_section.html
async function handler(event) {
  let newurl = `https://${websiteDomain}/open?screenType=home`; // Change the redirect URL to your choice
  let response = {
    statusCode: 301,
    statusDescription: "Redirect",
    headers: { location: { value: newurl } },
  };

  return response;
}
