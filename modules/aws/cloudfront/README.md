<!-- BEGIN_TF_DOCS -->
## Requirements

The following requirements are needed by this module:

- <a name="requirement_terraform"></a> [terraform](#requirement\_terraform) (>= 1.0)

- <a name="requirement_aws"></a> [aws](#requirement\_aws) (>= 5.0)

## Providers

The following providers are used by this module:

- <a name="provider_aws"></a> [aws](#provider\_aws) (>= 5.0)

- <a name="provider_aws.us_east_1"></a> [aws.us\_east\_1](#provider\_aws.us\_east\_1) (>= 5.0)

## Modules

The following Modules are called:

### <a name="module_this_s3_bucket"></a> [this\_s3\_bucket](#module\_this\_s3\_bucket)


## Resources

The following resources are used by this module:

- [aws_cloudfront_cache_policy.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/cloudfront_cache_policy) (resource)
- [aws_cloudfront_distribution.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/cloudfront_distribution) (resource)
- [aws_cloudfront_function.cdn_redirect_support](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/cloudfront_function) (resource)
- [aws_cloudfront_function.cdn_true_client_ip](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/cloudfront_function) (resource)
- [aws_cloudfront_function.content_security_policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/cloudfront_function) (resource)
- [aws_cloudfront_function.cross_site_redirect](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/cloudfront_function) (resource)
- [aws_cloudfront_monitoring_subscription.cloudfront_monitoring_subscription](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/cloudfront_monitoring_subscription) (resource)
- [aws_cloudfront_origin_access_identity.oai](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/cloudfront_origin_access_identity) (resource)
- [aws_cloudfront_response_headers_policy.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/cloudfront_response_headers_policy) (resource)
- [aws_cloudwatch_log_delivery.cloudwatch](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/cloudwatch_log_delivery) (resource)
- [aws_cloudwatch_log_delivery.s3](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/cloudwatch_log_delivery) (resource)
- [aws_cloudwatch_log_delivery_destination.cloudwatch](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/cloudwatch_log_delivery_destination) (resource)
- [aws_cloudwatch_log_delivery_destination.s3](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/cloudwatch_log_delivery_destination) (resource)
- [aws_cloudwatch_log_delivery_source.cloudfront_logs](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/cloudwatch_log_delivery_source) (resource)
- [aws_cloudwatch_log_group.cloudfront_logs](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/cloudwatch_log_group) (resource)
- [aws_cloudwatch_log_group.cloudfront_logs_us_east_1](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/cloudwatch_log_group) (resource)
- [aws_caller_identity.current](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/caller_identity) (data source)
- [aws_cloudfront_cache_policy.caching_disabled](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/cloudfront_cache_policy) (data source)
- [aws_cloudfront_cache_policy.caching_optimized](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/cloudfront_cache_policy) (data source)
- [aws_cloudfront_response_headers_policy.security_headers_policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/cloudfront_response_headers_policy) (data source)
- [aws_region.current](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/region) (data source)

## Required Inputs

The following input variables are required:

### <a name="input_account_name"></a> [account\_name](#input\_account\_name)

Description: Name of the account the distribution is in.

Type: `string`

### <a name="input_acm_certificate_arn"></a> [acm\_certificate\_arn](#input\_acm\_certificate\_arn)

Description: The ACM cert used for the custom domain. The cert must be in us-east-1.

Type: `string`

### <a name="input_cdn_custom_domain_names"></a> [cdn\_custom\_domain\_names](#input\_cdn\_custom\_domain\_names)

Description: Additional domain names for the distribution

Type: `list(string)`

### <a name="input_cf_origin"></a> [cf\_origin](#input\_cf\_origin)

Description: Whether the origin is alb or s3

Type: `string`

### <a name="input_comment"></a> [comment](#input\_comment)

Description: Description of the distribution

Type: `string`

### <a name="input_environment"></a> [environment](#input\_environment)

Description: Environment of the distribution; valid values = dev, stg, prd, cen.

Type: `string`

### <a name="input_origin_domain_name"></a> [origin\_domain\_name](#input\_origin\_domain\_name)

Description: Domain name of the origin.

Type: `string`

### <a name="input_origin_shield_region"></a> [origin\_shield\_region](#input\_origin\_shield\_region)

Description: Region for Origin Shield

Type: `string`

### <a name="input_service_name"></a> [service\_name](#input\_service\_name)

Description: Name of the service to be served by the distribution.

Type: `string`

### <a name="input_tags_basic"></a> [tags\_basic](#input\_tags\_basic)

Description: Additional tags to attach.

Type: `map(string)`

## Optional Inputs

The following input variables are optional (have default values):

### <a name="input_access_log_expire"></a> [access\_log\_expire](#input\_access\_log\_expire)

Description: Days before logs expire

Type: `number`

Default: `60`

### <a name="input_access_log_s3_object_lock_enabled"></a> [access\_log\_s3\_object\_lock\_enabled](#input\_access\_log\_s3\_object\_lock\_enabled)

Description: Whether S3 bucket should have an Object Lock configuration enabled.

Type: `bool`

Default: `true`

### <a name="input_access_log_transition_ia"></a> [access\_log\_transition\_ia](#input\_access\_log\_transition\_ia)

Description: Days before logs are transitioned to INFREQUENT\_ACCESS

Type: `number`

Default: `30`

### <a name="input_add_response_headers"></a> [add\_response\_headers](#input\_add\_response\_headers)

Description: List of custom headers to add to response headers policy

Type:

```hcl
list(object({
    header   = string
    override = bool
    value    = string
  }))
```

Default: `[]`

### <a name="input_allowed_methods"></a> [allowed\_methods](#input\_allowed\_methods)

Description: List of allowed methods to forward to the origin

Type: `set(string)`

Default:

```json
[
  "DELETE",
  "GET",
  "HEAD",
  "OPTIONS",
  "PATCH",
  "POST",
  "PUT"
]
```

### <a name="input_content_security_policies"></a> [content\_security\_policies](#input\_content\_security\_policies)

Description: Content security policy parameters in the following format, e.g. { 'defaultSrc': ['self', 'a.domain'], 'imageSrc': ['none', 'b.domain']}

Type: `map(list(string))`

Default:

```json
{
  "default-src": [
    "self"
  ],
  "frame-ancestors": [
    "self"
  ],
  "object-src": [
    "none"
  ]
}
```

### <a name="input_cors_access_control_allow_credentials"></a> [cors\_access\_control\_allow\_credentials](#input\_cors\_access\_control\_allow\_credentials)

Description: Included credentials in cross-origin requests

Type: `bool`

Default: `false`

### <a name="input_cors_access_control_allow_headers"></a> [cors\_access\_control\_allow\_headers](#input\_cors\_access\_control\_allow\_headers)

Description: Set of headers to allow in cross-origin requests

Type: `set(string)`

Default: `[]`

### <a name="input_cors_access_control_allow_methods"></a> [cors\_access\_control\_allow\_methods](#input\_cors\_access\_control\_allow\_methods)

Description: Set of methods to allow in cross-origin requests

Type: `set(string)`

Default: `[]`

### <a name="input_cors_access_control_allow_origins"></a> [cors\_access\_control\_allow\_origins](#input\_cors\_access\_control\_allow\_origins)

Description: Set of origins to allow in cross-origin requests

Type: `set(string)`

Default: `[]`

### <a name="input_cors_access_control_expose_headers"></a> [cors\_access\_control\_expose\_headers](#input\_cors\_access\_control\_expose\_headers)

Description: Set of headers to expose in cross-origin requests

Type: `set(string)`

Default: `[]`

### <a name="input_cors_access_control_max_age_sec"></a> [cors\_access\_control\_max\_age\_sec](#input\_cors\_access\_control\_max\_age\_sec)

Description: Maximum seconds preflight requests are cached

Type: `number`

Default: `5`

### <a name="input_cors_origin_override"></a> [cors\_origin\_override](#input\_cors\_origin\_override)

Description: Override origin

Type: `bool`

Default: `true`

### <a name="input_cross_site_redirection_domain"></a> [cross\_site\_redirection\_domain](#input\_cross\_site\_redirection\_domain)

Description: Domain to redirect

Type: `string`

Default: `""`

### <a name="input_custom_cache_policy"></a> [custom\_cache\_policy](#input\_custom\_cache\_policy)

Description: Use custom cache policy with [Host] header included in cache key

Type: `bool`

Default: `false`

### <a name="input_custom_error_responses"></a> [custom\_error\_responses](#input\_custom\_error\_responses)

Description: List of custom error responses

Type:

```hcl
list(object({
    error_caching_min_ttl = number
    error_code            = number
    response_code         = number
    response_page_path    = string
  }))
```

Default: `[]`

### <a name="input_default_root_object"></a> [default\_root\_object](#input\_default\_root\_object)

Description: Default root response object of the distribution

Type: `string`

Default: `null`

### <a name="input_enable_automatic_compression"></a> [enable\_automatic\_compression](#input\_enable\_automatic\_compression)

Description: Enable automatic compression for the origin

Type: `bool`

Default: `false`

### <a name="input_enable_cors"></a> [enable\_cors](#input\_enable\_cors)

Description: Enable CORS settings

Type: `bool`

Default: `false`

### <a name="input_enable_cross_site_redirect"></a> [enable\_cross\_site\_redirect](#input\_enable\_cross\_site\_redirect)

Description: To enable cross-site redirect viewer-request function, as defined by cross\_site\_redirection\_domain

Type: `bool`

Default: `false`

### <a name="input_enable_monitoring_subscription"></a> [enable\_monitoring\_subscription](#input\_enable\_monitoring\_subscription)

Description: Enable realtime logs for the distribution

Type: `bool`

Default: `false`

### <a name="input_enable_redirect_support"></a> [enable\_redirect\_support](#input\_enable\_redirect\_support)

Description: To enable redirect support viewer-response function

Type: `bool`

Default: `false`

### <a name="input_enable_true_client_ip"></a> [enable\_true\_client\_ip](#input\_enable\_true\_client\_ip)

Description: To enable forwarding real client IP to destination viewer-request function

Type: `bool`

Default: `false`

### <a name="input_extra_origins"></a> [extra\_origins](#input\_extra\_origins)

Description: List of additional origins, only supports S3 origins for now

Type:

```hcl
list(object({
    domain_name          = string
    origin_id            = string
    origin_shield_region = string
  }))
```

Default: `[]`

### <a name="input_geo_restriction_whitelist"></a> [geo\_restriction\_whitelist](#input\_geo\_restriction\_whitelist)

Description: List of allowed countries, in ISO 3166-1-alpha-2 code format, i.e. MY, SG, JP, etc

Type: `set(string)`

Default: `[]`

### <a name="input_lambda_function_associations"></a> [lambda\_function\_associations](#input\_lambda\_function\_associations)

Description: List of Lambda@Edge functions to attach to the origin

Type:

```hcl
list(object({
    event_type   = string
    lambda_arn   = string
    include_body = optional(bool, false)
  }))
```

Default: `[]`

### <a name="input_ordered_cache_behaviors"></a> [ordered\_cache\_behaviors](#input\_ordered\_cache\_behaviors)

Description: List of additional cache behaviours

Type:

```hcl
list(object({
    allowed_methods = set(string)
    cached_methods  = set(string)
    # Pass in empty string if no custom cache policy is required
    # If the `target_origin_id` is same as the default origin id, `cache_policy_id` will set as same as the default origin's cache policy. Any variable input will be ignored.
    cache_policy_id              = string
    enable_automatic_compression = optional(bool, false)
    path_pattern                 = string
    response_headers_policy_id   = string
    target_origin_id             = string
    viewer_protocol_policy       = string
  }))
```

Default: `[]`

### <a name="input_origin_custom_headers"></a> [origin\_custom\_headers](#input\_origin\_custom\_headers)

Description: List of custom headers to forward to origin

Type:

```hcl
list(object({
    name  = string
    value = string
  }))
```

Default: `[]`

### <a name="input_origin_keepalive_timeout"></a> [origin\_keepalive\_timeout](#input\_origin\_keepalive\_timeout)

Description: n/a

Type: `number`

Default: `5`

### <a name="input_origin_read_timeout"></a> [origin\_read\_timeout](#input\_origin\_read\_timeout)

Description: n/a

Type: `number`

Default: `30`

### <a name="input_redirect_logs_to_cloudwatch"></a> [redirect\_logs\_to\_cloudwatch](#input\_redirect\_logs\_to\_cloudwatch)

Description: Enable additional log redirection to CloudWatch

Type: `bool`

Default: `false`

### <a name="input_remove_response_headers"></a> [remove\_response\_headers](#input\_remove\_response\_headers)

Description: List of custom headers to remove from response headers policy

Type: `set(string)`

Default: `[]`

## Outputs

The following outputs are exported:

### <a name="output_cdn_arn"></a> [cdn\_arn](#output\_cdn\_arn)

Description: ARN of the distribution

### <a name="output_cdn_id"></a> [cdn\_id](#output\_cdn\_id)

Description: ID of the distribution

### <a name="output_domain_name"></a> [domain\_name](#output\_domain\_name)

Description: Domain name of the distribution

### <a name="output_hosted_zone_id"></a> [hosted\_zone\_id](#output\_hosted\_zone\_id)

Description: Hosted zone ID of the distribution

### <a name="output_iam_arn"></a> [iam\_arn](#output\_iam\_arn)

Description: If origin is S3, the IAM ARN of the OAI attached to the distribution
<!-- END_TF_DOCS -->
