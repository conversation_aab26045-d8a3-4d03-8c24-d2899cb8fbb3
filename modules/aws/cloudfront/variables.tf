## GENERAL
variable "environment" {
  description = "Environment of the distribution; valid values = dev, stg, prd, cen."
  type        = string
}

variable "account_name" {
  description = "Name of the account the distribution is in."
  type        = string
}

variable "service_name" {
  description = "Name of the service to be served by the distribution."
  type        = string
}

variable "tags_basic" {
  description = "Additional tags to attach."
  type        = map(string)
}

variable "comment" {
  description = "Description of the distribution"
  type        = string
}

variable "cdn_custom_domain_names" {
  description = "Additional domain names for the distribution"
  type        = list(string)
}

variable "default_root_object" {
  description = "Default root response object of the distribution"
  type        = string
  default     = null
  nullable    = true
}

variable "web_acl_id" {
  description = "ARN of WAFV2 to attach to the distribution -- ignore if FMS should manage it"
  type        = string
  default     = null
  nullable    = true
}

## CUSTOM ERROR RESPONSE
variable "custom_error_responses" {
  description = "List of custom error responses"
  type = list(object({
    error_caching_min_ttl = number
    error_code            = number
    response_code         = number
    response_page_path    = string
  }))
  default = []
}

## CACHE BEHAVIOURS
variable "allowed_methods" {
  description = "List of allowed methods to forward to the origin"
  type        = set(string)
  default     = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
}

variable "enable_automatic_compression" {
  description = "Enable automatic compression for the origin"
  type        = bool
  default     = false
}

variable "ordered_cache_behaviors" {
  description = "List of additional cache behaviours"
  type = list(object({
    allowed_methods = set(string)
    cached_methods  = set(string)
    # Pass in empty string if no custom cache policy is required
    # If the `target_origin_id` is same as the default origin id, `cache_policy_id` will set as same as the default origin's cache policy. Any variable input will be ignored.
    cache_policy_id              = string
    enable_automatic_compression = optional(bool, false)
    # Pass in empty string if no custom origin request policy is required
    # If the `target_origin_id` is same as the default origin id, `origin_request_policy_id` will set as same as the default origin's origin request policy. Any variable input will be ignored.
    origin_request_policy_id   = string
    path_pattern               = string
    response_headers_policy_id = string
    target_origin_id           = string
    viewer_protocol_policy     = string
  }))
  default = []
}

## CACHE BEHAVIOURS - FUNCTION ASSOCIATIONS
variable "enable_true_client_ip" {
  description = "To enable forwarding real client IP to destination viewer-request function"
  type        = bool
  default     = false

  # validation {
  #   condition     = var.enable_true_client_ip && !var.enable_cross_site_redirect
  #   error_message = "Only enable_true_client_ip or enable_cross_site_redirect can be enabled at once."
  # }
}

variable "enable_cross_site_redirect" {
  description = "To enable cross-site redirect viewer-request function, as defined by cross_site_redirection_domain"
  type        = bool
  default     = false

  # validation {
  #   condition     = var.enable_cross_site_redirect && !var.enable_true_client_ip
  #   error_message = "Only enable_cross_site_redirect or enable_true_client_ip can be enabled at once."
  # }
}

variable "cross_site_redirection_domain" {
  description = "Domain to redirect"
  type        = string
  default     = ""
}

variable "enable_redirect_support" {
  description = "To enable redirect support viewer-response function"
  type        = bool
  default     = false

  # validation {
  #   condition     = var.enable_redirect_support && !var.enable_security_headers
  #   error_message = "Only enable_redirect_support or enable_security_headers can be enabled at once."
  # }
}

variable "lambda_function_associations" {
  description = "List of Lambda@Edge functions to attach to the origin"
  type = list(object({
    event_type   = string
    lambda_arn   = string
    include_body = optional(bool, false)
  }))
  default = []
}

## ORIGIN
variable "cf_origin" {
  description = "Whether the origin is alb or s3"
  type        = string

  validation {
    condition     = var.cf_origin == "s3" || var.cf_origin == "alb"
    error_message = "The cf_origin can only be s3 or alb."
  }
}

variable "origin_domain_name" {
  description = "Domain name of the origin."
  type        = string
}

variable "origin_keepalive_timeout" {
  type    = number
  default = 5
}

variable "origin_read_timeout" {
  type    = number
  default = 30
}

variable "origin_custom_headers" {
  description = "List of custom headers to forward to origin"
  type = list(object({
    name  = string
    value = string
  }))
  default = []
}

variable "origin_shield_region" {
  description = "Region for Origin Shield"
  type        = string
  validation {
    condition = contains([
      "us-east-2", "us-east-1", "us-west-2", "ap-south-1", "ap-northeast-2", "ap-southeast-1", "ap-southeast-2",
      "ap-northeast-1", "eu-central-1", "eu-west-1", "eu-west-2", "sa-east-1", "me-central-1"
      ], var.origin_shield_region)
    error_message = "The origin_shield_region must be one of the valid regions."
  }
}

variable "extra_origins" {
  description = "List of additional origins, only supports S3 origins for now"
  type = list(object({
    domain_name          = string
    origin_id            = string
    origin_shield_region = string
  }))
  default = []
}

## RESTRICTIONS
variable "geo_restriction_whitelist" {
  description = "List of allowed countries, in ISO 3166-1-alpha-2 code format, i.e. MY, SG, JP, etc"
  type        = set(string)
  default     = []
}

## VIEWER CERTIFICATE
variable "acm_certificate_arn" {
  description = "The ACM cert used for the custom domain. The cert must be in us-east-1."
  type        = string
}

# CUSTOM CACHE POLICY
variable "custom_cache_policy" {
  type        = bool
  description = "Use custom cache policy with [Host] header included in cache key"
  default     = false
}

## CUSTOM RESPONSE HEADERS POLICY - CORS
variable "enable_cors" {
  description = "Enable CORS settings"
  type        = bool
  default     = false
}

variable "cors_access_control_allow_credentials" {
  description = "Included credentials in cross-origin requests"
  type        = bool
  default     = false
}

variable "cors_access_control_max_age_sec" {
  description = "Maximum seconds preflight requests are cached"
  type        = number
  default     = 5
}

variable "cors_access_control_allow_headers" {
  description = "Set of headers to allow in cross-origin requests"
  type        = set(string)
  default     = []
}

variable "cors_access_control_allow_methods" {
  description = "Set of methods to allow in cross-origin requests"
  type        = set(string)
  default     = []
}

variable "cors_access_control_allow_origins" {
  description = "Set of origins to allow in cross-origin requests"
  type        = set(string)
  default     = []
}

variable "cors_access_control_expose_headers" {
  description = "Set of headers to expose in cross-origin requests"
  type        = set(string)
  default     = []
}

variable "cors_origin_override" {
  description = "Override origin"
  type        = bool
  default     = true
}

## CUSTOM RESPONSE HEADERS POLICY - CUSTOMIZE RESPONSE HEADERS
variable "add_response_headers" {
  description = "List of custom headers to add to response headers policy"
  type = list(object({
    header   = string
    override = bool
    value    = string
  }))
  default = []
}

variable "remove_response_headers" {
  description = "List of custom headers to remove from response headers policy"
  type        = set(string)
  default     = []
}

## CUSTOM RESPONSE HEADERS POLICY - SECURITY HEADERS
variable "content_security_policies" {
  description = "Content security policy parameters in the following format, e.g. { 'defaultSrc': ['self', 'a.domain'], 'imageSrc': ['none', 'b.domain']}"
  type        = map(list(string))
  default = {
    "default-src"     = ["self"],
    "object-src"      = ["none"],
    "frame-ancestors" = ["self"],
  }

  # validation {
  #   condition     = var.content_security_policies
  #   error_message = "The CSP overrides can only accept string keys and list of strings for their values."
  # }
}

## CLOUDWATCH
variable "enable_monitoring_subscription" {
  description = "Enable realtime logs for the distribution"
  type        = bool
  default     = false
}

variable "redirect_logs_to_cloudwatch" {
  description = "Enable additional log redirection to CloudWatch"
  type        = bool
  default     = false
}

## S3
variable "access_log_transition_ia" {
  description = "Days before logs are transitioned to INFREQUENT_ACCESS"
  type        = number
  default     = 30
}

variable "access_log_expire" {
  description = "Days before logs expire"
  type        = number
  default     = 60
}

variable "access_log_s3_object_lock_enabled" {
  type        = bool
  description = "Whether S3 bucket should have an Object Lock configuration enabled."
  default     = true
}

variable "enable_s3_bucket_logging" {
  description = "Enable S3 bucket logging"
  type        = bool
  default     = true
}

variable "default_min_ttl" {
  description = "Minimum TTL for the cache"
  type        = number
  default     = 0
}

variable "default_max_ttl" {
  description = "Maximum TTL for the cache"
  type        = number
  default     = 31536000
}

variable "default_ttl" {
  description = "Default TTL for the cache"
  type        = number
  default     = 86400
}
