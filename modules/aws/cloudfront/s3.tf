module "this_s3_bucket" {
  source      = "./../s3"
  bucket_name = local.s3_logging_bucket_name
  lifecycle_rules = [
    {
      id      = "log"
      enabled = true

      expiration = {
        days = var.access_log_expire
      }

      filter = {}

      transition = {
        days          = var.access_log_transition_ia
        storage_class = "STANDARD_IA" # or "ONEZONE_IA"
      }
    }
  ]
  object_lock_enabled = var.access_log_s3_object_lock_enabled
  server_side_encryption_configuration = {
    rule = {
      apply_server_side_encryption_by_default = {
        sse_algorithm = "aws:kms"
        # kms_master_key_id = aws_kms_key.kms_key.arn
      }
    }
  }
  enable_s3_bucket_logging = var.enable_s3_bucket_logging
  s3_bucket_policy         = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect   = "Allow"
        Action   = [
          "s3:PutObject"
        ]
        Resource = [
          "arn:aws:s3:::${local.s3_logging_bucket_name}",
          "arn:aws:s3:::${local.s3_logging_bucket_name}/*",
        ]
        Principal = {
          Service = "delivery.logs.amazonaws.com"
        }
        Condition = {
          StringEquals = {
            "s3:x-amz-acl" = "bucket-owner-full-control",
            "aws:SourceAccount" = data.aws_caller_identity.current.account_id
          }
          ArnLike = {
            "aws:SourceArn" = "arn:aws:logs:us-east-1:${data.aws_caller_identity.current.account_id}:delivery-source:*"
          }
        }
      },
    ]
  })
  log_delivery_sources = [{
    account_id = data.aws_caller_identity.current.account_id
    region     = "us-east-1"
  }]
}

moved {
  from = module.sns_notification
  to   = module.this_s3_bucket.module.sns_notification
}

moved {
  from = module.sns_topic
  to   = module.this_s3_bucket.module.sns_topic
}

moved {
  from = aws_kms_key.kms_key
  to   = module.this_s3_bucket.aws_kms_key.this[0]
}

moved {
  from = aws_kms_alias.kms_alias
  to   = module.this_s3_bucket.aws_kms_alias.this
}
