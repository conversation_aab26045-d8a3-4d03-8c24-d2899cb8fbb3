output "cdn_id" {
  description = "ID of the distribution"
  value       = var.web_acl_id == null ? aws_cloudfront_distribution.this[0].id : aws_cloudfront_distribution.this_with_web_acl[0].id
}

output "cdn_arn" {
  description = "ARN of the distribution"
  value       = var.web_acl_id == null ? aws_cloudfront_distribution.this[0].arn : aws_cloudfront_distribution.this_with_web_acl[0].arn
}

output "domain_name" {
  description = "Domain name of the distribution"
  value       = var.web_acl_id == null ? aws_cloudfront_distribution.this[0].domain_name : aws_cloudfront_distribution.this_with_web_acl[0].domain_name
}

output "hosted_zone_id" {
  description = "Hosted zone ID of the distribution"
  value       = var.web_acl_id == null ? aws_cloudfront_distribution.this[0].hosted_zone_id : aws_cloudfront_distribution.this_with_web_acl[0].hosted_zone_id
}

output "iam_arn" {
  description = "If origin is S3, the IAM ARN of the OAI attached to the distribution"
  value       = var.cf_origin == "s3" ? aws_cloudfront_origin_access_identity.oai[0].iam_arn : ""
}


output "s3_logging_bucket_id" {
  description = "ID of the S3 bucket used for logging"
  value       = module.this_s3_bucket.s3_bucket_id
}

output "s3_logging_bucket_arn" {
  description = "ARN of the S3 bucket used for logging"
  value       = module.this_s3_bucket.s3_bucket_arn
}