# Real-time logs - https://docs.aws.amazon.com/AmazonCloudFront/latest/DeveloperGuide/real-time-logs.html
resource "aws_cloudfront_monitoring_subscription" "cloudfront_monitoring_subscription" {
  count           = var.enable_monitoring_subscription ? 1 : 0
  distribution_id = var.web_acl_id == null ? aws_cloudfront_distribution.this[0].id : aws_cloudfront_distribution.this_with_web_acl[0].id

  monitoring_subscription {
    realtime_metrics_subscription_config {
      realtime_metrics_subscription_status = "Enabled"
    }
  }
}

resource "aws_cloudwatch_log_group" "cloudfront_logs_us_east_1" {
  provider          = aws.us_east_1
  count             = var.redirect_logs_to_cloudwatch == true ? 1 : 0
  name              = "${var.service_name}-${var.environment}-cloudfront-logs"
  retention_in_days = 7
}

resource "aws_cloudwatch_log_delivery_source" "cloudfront_logs" {
  provider     = aws.us_east_1
  name         = "${var.environment}-${var.service_name}-log-delivery-src"
  log_type     = "ACCESS_LOGS"
  resource_arn = var.web_acl_id == null ? aws_cloudfront_distribution.this[0].arn : aws_cloudfront_distribution.this_with_web_acl[0].arn
}

# Pipe access logs into cloudwatch log group
resource "aws_cloudwatch_log_delivery" "cloudwatch" {
  provider                 = aws.us_east_1
  count                    = var.redirect_logs_to_cloudwatch ? 1 : 0
  delivery_source_name     = aws_cloudwatch_log_delivery_source.cloudfront_logs.name
  delivery_destination_arn = aws_cloudwatch_log_delivery_destination.cloudwatch[0].arn
}

resource "aws_cloudwatch_log_delivery_destination" "cloudwatch" {
  provider = aws.us_east_1
  count    = var.redirect_logs_to_cloudwatch ? 1 : 0
  name     = "${var.environment}-${var.service_name}-log-delivery-dst-cloudwatch"

  delivery_destination_configuration {
    destination_resource_arn = aws_cloudwatch_log_group.cloudfront_logs_us_east_1[0].arn
  }
}

# TODO: disable this until figure out why it doesnt work
# Store access logs into s3 bucket
# resource "aws_cloudwatch_log_delivery" "s3" {
#   provider                 = aws.us_east_1
#   delivery_source_name     = "${var.environment}-${var.service_name}-log-delivery-src"
#   delivery_destination_arn = module.this_s3_bucket.s3_bucket_arn
# }

resource "aws_cloudwatch_log_delivery_destination" "s3" {
  provider      = aws.us_east_1
  name          = "${var.environment}-${var.service_name}-log-delivery-dst-s3"
  output_format = "parquet"

  delivery_destination_configuration {
    destination_resource_arn = module.this_s3_bucket.s3_bucket_arn
  }
}

# https://docs.aws.amazon.com/AmazonCloudFront/latest/DeveloperGuide/standard-logging.html
resource "aws_cloudwatch_log_delivery_destination_policy" "s3" {
  provider                  = aws.us_east_1
  delivery_destination_name = aws_cloudwatch_log_delivery_destination.s3.name
  delivery_destination_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "CloudWatchLogDeliveryPolicy"
        Effect = "Allow"
        Principal = {
          AWS     = "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
          Service = "delivery.logs.amazonaws.com"
        }
        Action   = ["logs:CreateDelivery"]
        Resource = aws_cloudwatch_log_delivery_destination.s3.arn
      }
    ]
  })
}
