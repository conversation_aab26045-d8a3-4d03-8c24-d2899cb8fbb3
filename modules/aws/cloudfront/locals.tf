locals {
  origin_id                  = "${var.account_name}-${var.environment}-${var.service_name}-cloudfront-01"
  use_custom_response_policy = length(var.add_response_headers) > 0 || length(var.remove_response_headers) > 0 || var.enable_cors

  # Only cache for S3
  default_cache_policy_id = var.cf_origin == "s3" ? var.custom_cache_policy ? aws_cloudfront_cache_policy.this[0].id : data.aws_cloudfront_cache_policy.caching_optimized.id : data.aws_cloudfront_cache_policy.caching_disabled.id

  # Only use origin request policy ID for non-S3
  default_origin_request_policy_id = var.cf_origin == "s3" ? null : data.aws_cloudfront_origin_request_policy.all_viewer.id
  s3_logging_bucket_name = "${var.environment}-${var.service_name}-s3-access-log"
}
