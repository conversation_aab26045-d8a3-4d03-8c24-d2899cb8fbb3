resource "aws_cloudfront_origin_access_identity" "oai" {
  count   = var.cf_origin == "s3" ? 1 : 0
  comment = "Restrict to ${var.service_name} CloudFront"
}

resource "aws_cloudfront_function" "cdn_true_client_ip" {
  count   = var.enable_true_client_ip ? 1 : 0
  name    = "${var.environment}-${var.service_name}-true_client_ip"
  runtime = "cloudfront-js-2.0"
  code    = file("${path.module}/functions/true_client_ip.js")

  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_cloudfront_function" "cross_site_redirect" {
  count   = var.enable_cross_site_redirect ? 1 : 0
  name    = "${var.environment}-${var.service_name}-cross_site_redirection"
  runtime = "cloudfront-js-2.0"
  code = replace(
    file("${path.module}/functions/cross_site_redirection.js"),
    "$${websiteDomain}",
    var.cross_site_redirection_domain,
  )

  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_cloudfront_function" "content_security_policy" {
  count   = length(var.content_security_policies) > 0 && !var.enable_redirect_support ? 1 : 0
  name    = "${var.environment}-${var.service_name}-content_security_policy"
  runtime = "cloudfront-js-2.0"
  code = replace(
    file("${path.module}/functions/content_security_policy.js"),
    "$${CSP}",
    # join("; ", [for k, v in var.content_security_policies : "${k} ${join(" ", [for p in v : "'${p}'"])}"]),
    join("; ", [for k, v in var.content_security_policies :
      # Regex pattern is to detect host or scheme-like pattern, which if true, is mapped without quotes
      "${k} ${join(" ", [for p in v : length(regexall("(.*\\.*.*\\..*)|(:.+)", p)) > 0 ? p : "'${p}'"])}"
    ]),
  )

  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_cloudfront_function" "cdn_redirect_support" {
  count   = var.enable_redirect_support ? 1 : 0
  name    = "${var.environment}-${var.service_name}-redirect_support"
  runtime = "cloudfront-js-2.0"
  code = replace(
    replace(
      file("${path.module}/functions/redirect_support.js"),
      "$${CSP}",
      join("; ", [for k, v in var.content_security_policies :
        # Regex pattern is to detect host or scheme-like pattern, which if true, is mapped without quotes
        "${k} ${join(" ", [for p in v : length(regexall("(.*\\.*.*\\..*)|(:.+)", p)) > 0 ? p : "'${p}'"])}"
      ]),
    ),
    "$${websiteDomain}",
    var.cdn_custom_domain_names[0],
  )

  lifecycle {
    create_before_destroy = true
  }
}

moved {
  from = aws_cloudfront_cache_policy.cache_policy
  to   = aws_cloudfront_cache_policy.this
}

resource "aws_cloudfront_cache_policy" "this" {
  count       = var.custom_cache_policy ? 1 : 0
  name        = "${local.origin_id}-cache-policy"
  comment     = "${local.origin_id}-cache-policy"
  default_ttl = data.aws_cloudfront_cache_policy.caching_optimized.default_ttl
  max_ttl     = data.aws_cloudfront_cache_policy.caching_optimized.max_ttl
  min_ttl     = data.aws_cloudfront_cache_policy.caching_optimized.min_ttl

  parameters_in_cache_key_and_forwarded_to_origin {
    enable_accept_encoding_gzip   = data.aws_cloudfront_cache_policy.caching_optimized.parameters_in_cache_key_and_forwarded_to_origin[0].enable_accept_encoding_gzip
    enable_accept_encoding_brotli = data.aws_cloudfront_cache_policy.caching_optimized.parameters_in_cache_key_and_forwarded_to_origin[0].enable_accept_encoding_brotli

    cookies_config {
      cookie_behavior = data.aws_cloudfront_cache_policy.caching_optimized.parameters_in_cache_key_and_forwarded_to_origin[0].cookies_config[0].cookie_behavior
    }

    headers_config {
      header_behavior = "whitelist"

      headers {
        items = ["Host"]
      }
    }

    query_strings_config {
      query_string_behavior = data.aws_cloudfront_cache_policy.caching_optimized.parameters_in_cache_key_and_forwarded_to_origin[0].query_strings_config[0].query_string_behavior
    }
  }
}

resource "aws_cloudfront_response_headers_policy" "this" {
  name    = "${local.origin_id}-response-headers-policy"
  comment = var.comment

  dynamic "cors_config" {
    for_each = var.enable_cors ? [1] : []
    content {
      access_control_allow_credentials = var.cors_access_control_allow_credentials
      access_control_max_age_sec       = var.cors_access_control_max_age_sec
      origin_override                  = var.cors_origin_override

      access_control_allow_headers {
        items = var.cors_access_control_allow_headers
      }

      access_control_allow_methods {
        items = var.cors_access_control_allow_methods
      }

      access_control_allow_origins {
        items = var.cors_access_control_allow_origins
      }

      access_control_expose_headers {
        items = var.cors_access_control_expose_headers
      }
    }
  }

  dynamic "custom_headers_config" {
    for_each = length(var.add_response_headers) > 0 ? [1] : []
    content {
      dynamic "items" {
        for_each = var.add_response_headers
        content {
          header   = items.value.header
          override = items.value.override
          value    = items.value.value
        }
      }
    }
  }

  dynamic "remove_headers_config" {
    for_each = length(var.remove_response_headers) > 0 ? [1] : []
    content {
      dynamic "items" {
        for_each = var.remove_response_headers
        content {
          header = items.value
        }
      }
    }
  }

  dynamic "security_headers_config" {
    for_each = data.aws_cloudfront_response_headers_policy.security_headers_policy.security_headers_config
    content {
      dynamic "content_security_policy" {
        for_each = security_headers_config.value.content_security_policy
        content {
          content_security_policy = content_security_policy.value.content_security_policy
          override                = false
        }
      }

      dynamic "content_type_options" {
        for_each = security_headers_config.value.content_type_options
        content {
          override = true
        }
      }

      dynamic "frame_options" {
        for_each = security_headers_config.value.frame_options
        content {
          frame_option = frame_options.value.frame_option
          override     = true
        }
      }

      dynamic "referrer_policy" {
        for_each = security_headers_config.value.referrer_policy
        content {
          referrer_policy = referrer_policy.value.referrer_policy
          override        = true
        }
      }

      dynamic "strict_transport_security" {
        for_each = security_headers_config.value.strict_transport_security
        content {
          access_control_max_age_sec = 31536000
          include_subdomains         = true
          preload                    = true
          override                   = true
        }
      }

      dynamic "xss_protection" {
        for_each = security_headers_config.value.xss_protection
        content {
          mode_block = xss_protection.value.mode_block
          protection = xss_protection.value.protection
          report_uri = xss_protection.value.report_uri
          override   = true
        }
      }
    }
  }
}

moved {
  from = aws_cloudfront_distribution.cdn_distribution
  to   = aws_cloudfront_distribution.this[0]
}

resource "aws_cloudfront_distribution" "this" {
  count = var.web_acl_id == null ? 1 : 0

  aliases = var.cdn_custom_domain_names
  comment = var.comment
  # continuous_deployment_policy_id = TBD
  default_root_object = var.default_root_object
  enabled             = true
  is_ipv6_enabled     = true
  price_class         = "PriceClass_200"
  tags                = var.tags_basic

  dynamic "custom_error_response" {
    for_each = var.custom_error_responses
    content {
      error_caching_min_ttl = custom_error_response.value.error_caching_min_ttl
      error_code            = custom_error_response.value.error_code
      response_code         = custom_error_response.value.response_code
      response_page_path    = custom_error_response.value.response_page_path
    }
  }

  default_cache_behavior {
    allowed_methods = var.allowed_methods
    cached_methods  = ["GET", "HEAD", "OPTIONS"]
    # TODO: Verify if caching OPTIONS is mandatory, apparently some headers needs to be forwarded for S3
    # https://docs.aws.amazon.com/cloudfront/latest/APIReference/API_CachedMethods.html
    min_ttl                  = var.default_min_ttl
    max_ttl                  = var.default_max_ttl
    default_ttl              = var.default_ttl
    cache_policy_id          = local.default_cache_policy_id
    compress                 = var.enable_automatic_compression
    origin_request_policy_id = local.default_origin_request_policy_id
    # realtime_log_config_arn = TBD
    response_headers_policy_id = aws_cloudfront_response_headers_policy.this.id
    target_origin_id           = local.origin_id
    viewer_protocol_policy     = "redirect-to-https"

    dynamic "function_association" {
      for_each = var.enable_true_client_ip == true ? [1] : []
      content {
        event_type   = "viewer-request"
        function_arn = aws_cloudfront_function.cdn_true_client_ip[0].arn
      }
    }

    dynamic "function_association" {
      for_each = var.enable_cross_site_redirect == true ? [1] : []
      content {
        event_type   = "viewer-request"
        function_arn = aws_cloudfront_function.cross_site_redirect[0].arn
      }
    }

    dynamic "function_association" {
      for_each = length(var.content_security_policies) > 0 && !var.enable_redirect_support ? [1] : []
      content {
        event_type   = "viewer-response"
        function_arn = aws_cloudfront_function.content_security_policy[0].arn
      }
    }

    dynamic "function_association" {
      for_each = var.enable_redirect_support == true ? [1] : []
      content {
        event_type   = "viewer-response"
        function_arn = aws_cloudfront_function.cdn_redirect_support[0].arn
      }
    }

    dynamic "lambda_function_association" {
      for_each = var.lambda_function_associations == [] ? [] : var.lambda_function_associations
      content {
        event_type   = lambda_function_association.value.event_type
        lambda_arn   = lambda_function_association.value.lambda_arn
        include_body = lambda_function_association.value.include_body
      }
    }
  }

  dynamic "ordered_cache_behavior" {
    for_each = var.ordered_cache_behaviors
    content {
      allowed_methods            = ordered_cache_behavior.value.allowed_methods
      cached_methods             = ordered_cache_behavior.value.cached_methods
      cache_policy_id            = ordered_cache_behavior.value.target_origin_id == local.origin_id ? local.default_cache_policy_id : ordered_cache_behavior.value.cache_policy_id
      compress                   = ordered_cache_behavior.value.enable_automatic_compression
      origin_request_policy_id   = ordered_cache_behavior.value.target_origin_id == local.origin_id ? local.default_origin_request_policy_id : ordered_cache_behavior.value.origin_request_policy_id
      path_pattern               = ordered_cache_behavior.value.path_pattern
      response_headers_policy_id = ordered_cache_behavior.value.response_headers_policy_id == "" ? aws_cloudfront_response_headers_policy.this.id : ordered_cache_behavior.value.response_headers_policy_id
      target_origin_id           = ordered_cache_behavior.value.target_origin_id
      viewer_protocol_policy     = ordered_cache_behavior.value.viewer_protocol_policy
    }
  }

  origin {
    # connection_attempts = TBD
    # connection_timeout = TBD
    domain_name = var.origin_domain_name
    # origin_access_control_id = TBD
    origin_id = local.origin_id
    # origin_path = TBD

    # TODO: Move out from custom origins
    dynamic "custom_origin_config" {
      for_each = var.cf_origin == "alb" ? [1] : []
      content {
        http_port                = 80
        https_port               = 443
        origin_protocol_policy   = "https-only"
        origin_ssl_protocols     = ["TLSv1.2"]
        origin_keepalive_timeout = var.origin_keepalive_timeout
        origin_read_timeout      = var.origin_read_timeout
      }
    }

    dynamic "custom_header" {
      for_each = var.origin_custom_headers
      content {
        name  = custom_header.value.name
        value = custom_header.value.value
      }
    }

    origin_shield {
      enabled              = true
      origin_shield_region = var.origin_shield_region
    }

    dynamic "s3_origin_config" {
      for_each = var.cf_origin == "s3" ? [1] : []
      content {
        origin_access_identity = aws_cloudfront_origin_access_identity.oai[0].cloudfront_access_identity_path
      }
    }

    # TODO: Switch ALB to VPC Origin
    # dynamic "vpc_origin_config" {
    #   for_each =
    # }
  }

  dynamic "origin" {
    for_each = var.extra_origins
    content {
      domain_name = origin.value.domain_name
      origin_id   = origin.value.origin_id

      origin_shield {
        enabled              = true
        origin_shield_region = origin.value.origin_shield_region
      }

      s3_origin_config {
        origin_access_identity = aws_cloudfront_origin_access_identity.oai[0].cloudfront_access_identity_path
      }
    }
  }

  restrictions {
    geo_restriction {
      locations        = var.geo_restriction_whitelist
      restriction_type = length(var.geo_restriction_whitelist) == 0 ? "none" : "whitelist"
    }
  }

  viewer_certificate {
    cloudfront_default_certificate = false
    acm_certificate_arn            = var.acm_certificate_arn
    minimum_protocol_version       = "TLSv1.2_2021"
    ssl_support_method             = "sni-only"
  }

  lifecycle {
    ignore_changes = [web_acl_id]
  }
}

resource "aws_cloudfront_distribution" "this_with_web_acl" {
  count = var.web_acl_id != null ? 1 : 0

  aliases = var.cdn_custom_domain_names
  comment = var.comment
  # continuous_deployment_policy_id = TBD
  default_root_object = var.default_root_object
  enabled             = true
  is_ipv6_enabled     = true
  price_class         = "PriceClass_200"
  web_acl_id          = var.web_acl_id
  tags                = var.tags_basic

  dynamic "custom_error_response" {
    for_each = var.custom_error_responses
    content {
      error_caching_min_ttl = custom_error_response.value.error_caching_min_ttl
      error_code            = custom_error_response.value.error_code
      response_code         = custom_error_response.value.response_code
      response_page_path    = custom_error_response.value.response_page_path
    }
  }

  default_cache_behavior {
    allowed_methods = var.allowed_methods
    cached_methods  = ["GET", "HEAD", "OPTIONS"]
    # TODO: Verify if caching OPTIONS is mandatory, apparently some headers needs to be forwarded for S3
    # https://docs.aws.amazon.com/cloudfront/latest/APIReference/API_CachedMethods.html
    cache_policy_id          = local.default_cache_policy_id
    compress                 = var.enable_automatic_compression
    origin_request_policy_id = local.default_origin_request_policy_id
    # realtime_log_config_arn = TBD
    response_headers_policy_id = aws_cloudfront_response_headers_policy.this.id
    target_origin_id           = local.origin_id
    viewer_protocol_policy     = "redirect-to-https"

    dynamic "function_association" {
      for_each = var.enable_true_client_ip == true ? [1] : []
      content {
        event_type   = "viewer-request"
        function_arn = aws_cloudfront_function.cdn_true_client_ip[0].arn
      }
    }

    dynamic "function_association" {
      for_each = var.enable_cross_site_redirect == true ? [1] : []
      content {
        event_type   = "viewer-request"
        function_arn = aws_cloudfront_function.cross_site_redirect[0].arn
      }
    }

    dynamic "function_association" {
      for_each = length(var.content_security_policies) > 0 && !var.enable_redirect_support ? [1] : []
      content {
        event_type   = "viewer-response"
        function_arn = aws_cloudfront_function.content_security_policy[0].arn
      }
    }

    dynamic "function_association" {
      for_each = var.enable_redirect_support == true ? [1] : []
      content {
        event_type   = "viewer-response"
        function_arn = aws_cloudfront_function.cdn_redirect_support[0].arn
      }
    }

    dynamic "lambda_function_association" {
      for_each = var.lambda_function_associations == [] ? [] : var.lambda_function_associations
      content {
        event_type   = lambda_function_association.value.event_type
        lambda_arn   = lambda_function_association.value.lambda_arn
        include_body = lambda_function_association.value.include_body
      }
    }
  }

  dynamic "ordered_cache_behavior" {
    for_each = var.ordered_cache_behaviors
    content {
      allowed_methods            = ordered_cache_behavior.value.allowed_methods
      cached_methods             = ordered_cache_behavior.value.cached_methods
      cache_policy_id            = ordered_cache_behavior.value.target_origin_id == local.origin_id ? local.default_cache_policy_id : ordered_cache_behavior.value.cache_policy_id
      compress                   = ordered_cache_behavior.value.enable_automatic_compression
      origin_request_policy_id   = ordered_cache_behavior.value.target_origin_id == local.origin_id ? local.default_origin_request_policy_id : ordered_cache_behavior.value.origin_request_policy_id
      path_pattern               = ordered_cache_behavior.value.path_pattern
      response_headers_policy_id = ordered_cache_behavior.value.response_headers_policy_id == "" ? aws_cloudfront_response_headers_policy.this.id : ordered_cache_behavior.value.response_headers_policy_id
      target_origin_id           = ordered_cache_behavior.value.target_origin_id
      viewer_protocol_policy     = ordered_cache_behavior.value.viewer_protocol_policy
    }
  }

  origin {
    # connection_attempts = TBD
    # connection_timeout = TBD
    domain_name = var.origin_domain_name
    # origin_access_control_id = TBD
    origin_id = local.origin_id
    # origin_path = TBD

    # TODO: Move out from custom origins
    dynamic "custom_origin_config" {
      for_each = var.cf_origin == "alb" ? [1] : []
      content {
        http_port                = 80
        https_port               = 443
        origin_protocol_policy   = "https-only"
        origin_ssl_protocols     = ["TLSv1.2"]
        origin_keepalive_timeout = var.origin_keepalive_timeout
        origin_read_timeout      = var.origin_read_timeout
      }
    }

    dynamic "custom_header" {
      for_each = var.origin_custom_headers
      content {
        name  = custom_header.value.name
        value = custom_header.value.value
      }
    }

    origin_shield {
      enabled              = true
      origin_shield_region = var.origin_shield_region
    }

    dynamic "s3_origin_config" {
      for_each = var.cf_origin == "s3" ? [1] : []
      content {
        origin_access_identity = aws_cloudfront_origin_access_identity.oai[0].cloudfront_access_identity_path
      }
    }

    # TODO: Switch ALB to VPC Origin
    # dynamic "vpc_origin_config" {
    #   for_each =
    # }
  }

  dynamic "origin" {
    for_each = var.extra_origins
    content {
      domain_name = origin.value.domain_name
      origin_id   = origin.value.origin_id

      origin_shield {
        enabled              = true
        origin_shield_region = origin.value.origin_shield_region
      }

      s3_origin_config {
        origin_access_identity = aws_cloudfront_origin_access_identity.oai[0].cloudfront_access_identity_path
      }
    }
  }

  restrictions {
    geo_restriction {
      locations        = var.geo_restriction_whitelist
      restriction_type = length(var.geo_restriction_whitelist) == 0 ? "none" : "whitelist"
    }
  }

  viewer_certificate {
    cloudfront_default_certificate = false
    acm_certificate_arn            = var.acm_certificate_arn
    minimum_protocol_version       = "TLSv1.2_2021"
    ssl_support_method             = "sni-only"
  }
}
