resource "aws_security_group" "new_rule" {
  vpc_id = var.vpc_id
  name   = var.security_group_name
}

resource "aws_security_group_rule" "this" {
  for_each          = var.security_group_rules
  type              = each.value.type
  from_port         = each.value.from_port
  to_port           = each.value.to_port
  protocol          = each.value.protocol
  cidr_blocks       = each.value.cidr
  security_group_id = aws_security_group.new_rule.id
}
