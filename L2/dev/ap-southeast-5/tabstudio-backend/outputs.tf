output "ecs_service" {
  description = "ID of the ECS service, [id, name]"
  value = {
    id   = aws_ecs_service.this.id
    name = aws_ecs_service.this.name
  }
  sensitive = false
}

output "task_definition" {
  description = "ARN of the task definition, [arn, family]"
  value = {
    arn    = aws_ecs_task_definition.this.arn
    family = aws_ecs_task_definition.this.family
  }
  sensitive = false
}

output "media_raw_s3" {
  description = "Name of the S3 bucket, [arn, name, id, domain_name]"
  value = {
    name        = module.s3_media_raw.s3_bucket_name
    arn         = module.s3_media_raw.s3_bucket_arn
    id          = module.s3_media_raw.s3_bucket_id
    domain_name = module.s3_media_raw.s3_bucket_regional_domain_name
  }
  sensitive = false
}

output "media_processed_s3" {
  description = "Name of the S3 bucket, [arn, name, id, domain_name]"
  value = {
    name        = module.s3_media_processed.s3_bucket_name
    arn         = module.s3_media_processed.s3_bucket_arn
    id          = module.s3_media_processed.s3_bucket_id
    domain_name = module.s3_media_processed.s3_bucket_regional_domain_name
  }
  sensitive = false
}

output "media_raw_s3_kms" {
  description = "ARN of the KMS key, [arn]"
  value = {
    arn = module.s3_media_raw.s3_kms_key_arn
  }
  sensitive = false
}

output "media_processed_s3_kms" {
  description = "ARN of the KMS key, [arn]"
  value = {
    arn = module.s3_media_processed.s3_kms_key_arn
  }
  sensitive = false
}

output "media_raw_s3_sns_topic" {
  description = "ARN of the SNS topic for raw media bucket events"
  value = {
    arn = module.s3_media_raw.sns_topic_arn
  }
  sensitive = false
}

output "s3_event_queue" {
  description = "Details of the S3 event SQS queue"
  value = {
    arn = module.sqs_queues["s3-event"].queue_arn
    url = module.sqs_queues["s3-event"].queue_url
    name = module.sqs_queues["s3-event"].queue_name
  }
  sensitive = false
}

output "s3_events_sns_subscription" {
  description = "ARN of the SNS subscription for S3 events to SQS"
  value = {
    arn = aws_sns_topic_subscription.s3_events_to_sqs.arn
  }
  sensitive = false
}

output "ecs_task_role" {
  description = "ARN of the ECS task role, [arn, name]"
  value = {
    arn  = aws_iam_role.ecs_task_role.arn
    name = aws_iam_role.ecs_task_role.name
  }
  sensitive = false
}

output "sqs_queues" {
  description = "ARN of the SQS queue, [arn, name]"
  value = {
    for k, v in module.sqs_queues : k => {
      queue_id = v.queue_id
      arn      = v.queue_arn
      name     = v.queue_name
      url      = v.queue_url
    }
  }
  sensitive = false
}

output "postgres_rds" {
  description = "ARN of the RDS instance, [arn, name]"
  value = {
    arn                    = module.postgres_rds.db_instance_arn
    name                   = module.postgres_rds.db_instance_name
    domain_fqdn            = module.postgres_rds.db_instance_domain_fqdn
    port                   = module.postgres_rds.db_instance_port
    master_user_secret_arn = module.postgres_rds.db_instance_master_user_secret_arn
    db_parameter_group_id  = module.postgres_rds.db_parameter_group_id
    db_parameter_group_arn = module.postgres_rds.db_parameter_group_arn
    cloudwatch_log_groups  = module.postgres_rds.db_instance_cloudwatch_log_groups
    domain_auth_secret_arn = module.postgres_rds.db_instance_domain_auth_secret_arn

  }
  sensitive = false
}

output "bastion_host_sg" {
  description = "ID of the bastion host security group"
  value       = {
    id = aws_security_group.bastion_host.id
    arn = aws_security_group.bastion_host.arn
  }
  sensitive   = false
}
