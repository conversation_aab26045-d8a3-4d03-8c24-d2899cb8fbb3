resource "aws_ecs_service" "this" {
  name            = var.ecs_service_name
  cluster         = data.terraform_remote_state.ecs.outputs.cluster_arns["tabstudio-dev-01"]
  task_definition = aws_ecs_task_definition.this.arn # 👈 dummy arn only, CI will register revisions
  desired_count   = var.ecs_desired_count
  launch_type     = "FARGATE"

  network_configuration {
    subnets          = data.terraform_remote_state.subnet.outputs.private_subnet_ids
    security_groups  = concat([aws_security_group.this.id], var.ecs_extra_security_groups)
    assign_public_ip = false
  }

  load_balancer {
    target_group_arn = data.terraform_remote_state.alb.outputs.public_alb_target_group_arns[var.ecs_alb_target_group]
    container_name   = var.ecs_container_name
    container_port   = var.ecs_container_port
  }

  deployment_minimum_healthy_percent = 50
  deployment_maximum_percent         = 200

  propagate_tags = "SERVICE"
  lifecycle {
    ignore_changes = [
      task_definition,
    ]
  }
  tags = {
    Name = "tabstudio-backend"
  }
}

# add a task definition using httpbin image
resource "aws_ecs_task_definition" "this" {
  family                   = var.ecs_task_definition_family
  network_mode             = "awsvpc"
  requires_compatibilities = ["FARGATE"]
  cpu                      = "256"
  memory                   = "512"
  execution_role_arn       = data.terraform_remote_state.ecs.outputs.task_execution_role_arns["tabstudio-dev-01"]
  container_definitions = jsonencode([
    {
      name  = var.ecs_container_name
      image = var.ecs_ecr_image_url
      portMappings = [
        {
          containerPort = 8080
          protocol      = "tcp"
        }
      ]
      environment = [
        {
          name  = "PORT"
          value = "8080"
        }
      ]
      tags = {
        Name = "${local.env}-tabstudio-backend"
      }
    }
    ]
  )
  tags = {
    Name = "${local.env}-tabstudio-backend"
  }
}

# Create CloudWatch Log Group
resource "aws_cloudwatch_log_group" "this" {
  name              = "/ecs/${var.ecs_task_definition_family}"
  retention_in_days = 7

  tags = {
    Name = "${local.env}-${var.ecs_service_name}-app-logs"
  }
}

resource "aws_security_group" "this" {
  name        = "${local.env}-tabstudio-backend-sg"
  description = "Security group for tabstudio app"
  vpc_id      = data.terraform_remote_state.vpc.outputs.vpc_id

  ingress {
    description     = "Allow traffic from ALB to ECS tasks"
    security_groups = [data.terraform_remote_state.alb.outputs.public_alb_security_group_id]
    from_port       = var.ecs_container_port
    to_port         = var.ecs_container_port
    protocol        = "tcp"
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
}
