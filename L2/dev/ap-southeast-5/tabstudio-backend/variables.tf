variable "account_id" {
  type = string
}

# ecs service
variable "ecs_service_name" {
  type        = string
  description = "Name of the ECS service"
}

variable "ecs_task_definition_family" {
  type        = string
  description = "Task definition family name (CI registers new revs)"
}

variable "ecs_desired_count" {
  type    = number
  default = 1
}

variable "ecs_extra_security_groups" {
  type        = list(string)
  description = "List of SGs for the service"
  default     = []
}
#
# variable "target_group_arn" {
#   type        = string
#   description = "ALB target group ARN"
# }

variable "ecs_container_name" {
  type        = string
  description = "Container name in the task definition"
}

variable "ecs_container_port" {
  type        = number
  description = "Container port exposed"
}

variable "ecs_ecr_image_url" {
  type        = string
  description = "ECR image URL"
}

variable "ecs_alb_target_group" {
  type        = string
  description = "ALB target group name"
}


# consolidate the sqs variables in a map object with default value
variable "sqs_queues" {
  type = map(object({
    kms_master_key_id          = string
    visibility_timeout_seconds = optional(number)
    message_retention_seconds  = optional(number)
    receive_wait_time_seconds  = optional(number)
    redrive_policy = object({
      deadLetterTargetArn = string
      maxReceiveCount     = optional(number)
    })
    is_dlq      = bool
    permissions = list(string)
    enabled     = bool
  }))
  default = {
    request_handler = {
      kms_master_key_id          = "TF_VAR_kms_master_key_id"
      redrive_policy = {
        deadLetterTargetArn = "TF_VAR_redrive_policy_dead_letter_target_arn"
      }
      is_dlq = false
      permissions = [
        "sqs:SendMessage",
        "sqs:ReceiveMessage",
        "sqs:DeleteMessage",
        "sqs:ChangeMessageVisibility",
        "sqs:ListQueues",
        "sqs:GetQueueUrl",
        "sqs:GetQueueAttributes",
        "sqs:SetQueueAttributes",
        "sqs:SendMessageBatch"
      ]
      dlq_permissions = [
        "sqs:SendMessage",
        "sqs:ReceiveMessage",
        "sqs:DeleteMessage",
        "sqs:ChangeMessageVisibility"
      ]
      enabled = false
    }
  }
}
