account_id = "************"

# ecs service
ecs_service_name           = "tabstudio-backend"
ecs_task_definition_family = "dev-tabstudio-backend"
ecs_desired_count          = 1
ecs_container_name         = "tabstudio-backend"
ecs_container_port         = 8080
# todo: revert to our own ecr url
# ecs_ecr_image_url          = "************.dkr.ecr.ap-southeast-5.amazonaws.com/tabstudio-backend"
ecs_ecr_image_url    = "ghcr.io/mccutchen/go-httpbin:2.18.3"
ecs_alb_target_group = "dev-tabstudio-backend"

sqs_queues = {
  request-handler = {
    kms_master_key_id = "TF_VAR_kms_master_key_id"
    redrive_policy = {
      deadLetterTargetArn = "arn:aws:sqs:ap-southeast-5:************:dev-tabstudio-tabstudio-backend-request-handler-dlq-sqs"
      maxReceiveCount     = 20
    }
    permissions = [
      "sqs:SendMessage",
      "sqs:ReceiveMessage",
      "sqs:DeleteMessage",
      "sqs:ChangeMessageVisibility",
      "sqs:ListQueues",
      "sqs:GetQueueUrl",
      "sqs:GetQueueAttributes",
      "sqs:SetQueueAttributes",
      "sqs:SendMessageBatch"
    ]
    is_dlq  = false
    enabled = true
  },
  request-handler-dlq = {
    kms_master_key_id = "TF_VAR_kms_master_key_id"
    redrive_policy = {
      deadLetterTargetArn = "TF_VAR_redrive_policy_dead_letter_target_arn"
    }
    permissions = [
      "sqs:SendMessage",
      "sqs:ReceiveMessage",
      "sqs:DeleteMessage",
      "sqs:ChangeMessageVisibility"
    ]
    is_dlq  = true
    enabled = true
  },
  response-handler = {
    kms_master_key_id = "TF_VAR_kms_master_key_id"
    redrive_policy = {
      deadLetterTargetArn = "arn:aws:sqs:ap-southeast-5:************:dev-tabstudio-tabstudio-backend-response-handler-dlq-sqs"
      maxReceiveCount     = 20
    }
    permissions = [
      "sqs:SendMessage",
      "sqs:ReceiveMessage",
      "sqs:DeleteMessage",
      "sqs:ChangeMessageVisibility",
      "sqs:ListQueues",
      "sqs:GetQueueUrl",
      "sqs:GetQueueAttributes",
      "sqs:SetQueueAttributes",
      "sqs:SendMessageBatch"
    ]
    is_dlq  = false
    enabled = true
  },
  response-handler-dlq = {
    kms_master_key_id = "TF_VAR_kms_master_key_id"
    redrive_policy = {
      deadLetterTargetArn = "TF_VAR_redrive_policy_dead_letter_target_arn"
    }
    permissions = [
      "sqs:SendMessage",
      "sqs:ReceiveMessage",
      "sqs:DeleteMessage",
      "sqs:ChangeMessageVisibility"
    ]
    is_dlq  = true
    enabled = true
  },
  s3-event = {
    kms_master_key_id = "TF_VAR_kms_master_key_id"
    redrive_policy = {
      deadLetterTargetArn = "arn:aws:sqs:ap-southeast-5:************:dev-tabstudio-tabstudio-backend-s3-event-dlq-sqs"
      maxReceiveCount     = 20
    }
    permissions = [
      "sqs:SendMessage",
      "sqs:ReceiveMessage",
      "sqs:DeleteMessage",
      "sqs:ChangeMessageVisibility",
      "sqs:ListQueues",
      "sqs:GetQueueUrl",
      "sqs:GetQueueAttributes",
      "sqs:SetQueueAttributes",
      "sqs:SendMessageBatch"
    ]
    is_dlq  = false
    enabled = true
  },
  s3-event-dlq = {
    kms_master_key_id = "TF_VAR_kms_master_key_id"
    redrive_policy = {
      deadLetterTargetArn = "TF_VAR_redrive_policy_dead_letter_target_arn"
    }
    permissions = [
      "sqs:SendMessage",
      "sqs:ReceiveMessage",
      "sqs:DeleteMessage",
      "sqs:ChangeMessageVisibility"
    ]
    is_dlq  = true
    enabled = true
  },
}
