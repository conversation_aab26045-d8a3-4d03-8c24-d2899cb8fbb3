module "sqs_queues" {
  for_each = { for k, v in var.sqs_queues : k => v if v.enabled }
  source = "../../../../modules/aws/sqs"

  name                       = "${local.env}-${local.project}-${local.service_name}-${each.key}-sqs"
  kms_master_key_id          = each.value.kms_master_key_id == "TF_VAR_kms_master_key_id" ? null : each.value.kms_master_key_id
  visibility_timeout_seconds = each.value.visibility_timeout_seconds == null ? 30 : each.value.visibility_timeout_seconds
  message_retention_seconds  = each.value.message_retention_seconds == null ? 345600 : each.value.message_retention_seconds
  receive_wait_time_seconds  = each.value.receive_wait_time_seconds == null ? 0 : each.value.receive_wait_time_seconds
  redrive_policy             = each.value.redrive_policy.deadLetterTargetArn == "TF_VAR_redrive_policy_dead_letter_target_arn" ? "" : jsonencode(each.value.redrive_policy)

  tags = {
    tag_env               = local.env
    tag_component_name    = "sqs"
    tags_application_team = local.service_name
    tags_cost_center      = "engineering"
  }
}
