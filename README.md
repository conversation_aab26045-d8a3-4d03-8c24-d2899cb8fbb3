# tabstudio-infra

Infrastructure-as-Code (IaC) for the Tabstudio infrastructure stack.

This repository includes a bootstrap utility to initialize the AWS resources required for Terraform remote state and locking, and to create an IAM role Terraform can assume.

## Contents
- tools/bootstrap-tf.py — CLI to create and configure:
  - S3 bucket for Terraform state: `tabstudio-terraform-state-<env>` (versioning + AES256 encryption)
  - DynamoDB table for state locks: `tabstudio-terraform-locks-<env>`
  - IAM role: `terraform-<env>` with an inline policy and trust to an admin role. All terraform plan and apply operations should be run with this role.

## Prerequisites
- Python 3.10+
- boto3 and colorlog (install via pip)
- AWS credentials/profile with permissions to create S3, DynamoDB, and IAM resources
- Known AWS Region and Environment (one of: `dev`, `stg`, `prd`)

Optional but recommended:
- AWS SSO setup. The script expects to trust an SSO admin role (see Notes below).
- `aws configure sso`

### Install dependencies (example)
```bash
python3 -m venv .venv
source .venv/bin/activate
pip install -r requirements.txt
```

Installing via pyenv and pyenv-virtualenv
```bash
pyenv local
pyenv virtualenv .venv
pyenv activate .venv
pyenv exec pip install -r requirements.txt
```

## Bootstrap Script Usage
Path: `tools/bootstrap-tf.py`

General form:
```bash
python tools/bootstrap-tf.py \
  --profile <aws_profile> \
  --region <aws_region> \
  --env <dev|stg|prd> \
  --action <create-s3|create-ddb|create-role|attach-policy|assume-role|inspect-sessions|all>
```

Arguments:
- `--profile` (required): AWS config/credentials profile to use
- `--region` (required): AWS region (e.g. `ap-southeast-5`)
- `--env` (required): Environment name (`dev`, `stg`, `prd`)
- `--action` (required): One of the supported actions below

### Supported actions
- `create-s3`: Creates the Terraform state S3 bucket `tabstudio-terraform-state-<env>`
  - Enables bucket versioning and AES256 default encryption
- `create-ddb`: Creates the DynamoDB table `tabstudio-terraform-locks-<env>` (PK: `LockID`)
- `create-role`: Creates IAM role `terraform-<env>` with a trust policy for an admin role
- `attach-policy`: Attaches/updates an inline policy on `terraform-<env>` granting access to the bucket and table
- `assume-role`: Assumes the `terraform-<env>` role and prints export commands for temporary credentials. This is more for verifying the trust policy and role permissions.
- `inspect-sessions`: Prints the current caller/account information for the session
- `all`: Runs `create-s3`, `create-ddb`, `create-role`, and `attach-policy` in order

### Examples
Create only the S3 bucket for dev:
```bash
python tools/bootstrap-tf.py --profile my-sso --region ap-southeast-5 --env dev --action create-s3
```

Create DDB table for stg:
```bash
python tools/bootstrap-tf.py --profile my-sso --region ap-southeast-5 --env stg --action create-ddb
```

Create role and attach inline policy (prd):
```bash
python tools/bootstrap-tf.py --profile my-sso --region ap-southeast-5 --env prd --action create-role
python tools/bootstrap-tf.py --profile my-sso --region ap-southeast-5 --env prd --action attach-policy
```

Run everything for dev:
```bash
python tools/bootstrap-tf.py --profile my-sso --region ap-southeast-5 --env dev --action all
```

Assume the terraform role and export credentials into your shell:
```bash
python tools/bootstrap-tf.py --profile my-sso --region ap-southeast-5 --env dev --action assume-role
# Example output (copy/paste to export)
export AWS_ACCESS_KEY_ID=...
export AWS_SECRET_ACCESS_KEY=...
export AWS_SESSION_TOKEN=...
```

Inspect the current session (whoami/account):
```bash
python tools/bootstrap-tf.py --profile my-sso --region ap-southeast-5 --env dev --action inspect-sessions
```

## Using with Terraform
After bootstrapping, configure your Terraform backend like this (example):
```hcl
terraform {
  backend "s3" {
    bucket         = "tabstudio-terraform-state-dev"
    key            = "<module-dir-path>/terraform.tfstate"
    region         = "ap-southeast-5"
    dynamodb_table = "tabstudio-terraform-locks-dev"
    encrypt        = true
  }
}
```

If you used `assume-role`, ensure the exported AWS_* environment variables are present in the shell that runs `terraform init/plan/apply`.

## Notes and Defaults
- Regions and names are derived from templates in the script:
  - Bucket: `tabstudio-terraform-state-<env>`
  - DynamoDB: `tabstudio-terraform-locks-<env>`
  - Role: `terraform-<env>`
- Environments supported: `dev`, `stg`, `prd`
- The trust policy uses an SSO Admin role ARN template embedded in the script:
  - `ADMIN_ROLE_ARN_TEMPLATE = "arn:aws:iam::<ACCOUNT_ID>:role/aws-reserved/sso.amazonaws.com/ap-southeast-5/AWSReservedSSO_AdministratorAccess_416f3c4e29e5a28c"`
  - The script replaces `<ACCOUNT_ID>` at runtime; update the template if your SSO path or permission set suffix differs.
  - To discover your admin role ARN, you can run: `aws iam list-roles --query "Roles[?contains(RoleName, 'AWSReservedSSO_AdministratorAccess')].Arn" --output text --profile <profile>`
- Policies: The inline policy grants access to the S3 bucket and DynamoDB table. Review and tighten as needed for your org’s security posture.

## Troubleshooting
- Missing permissions: Ensure the `--profile` you use can create S3, DynamoDB, and IAM resources.
- Bucket already exists: The script continues if the bucket is already owned by you.
- DDB table exists: The script continues if the table exists.
- AssumeRole failures: Verify the trust relationship and that your caller identity can assume `terraform-<env>`.
- Region mismatches: Create resources and run Terraform in the same region specified by `--region`.

